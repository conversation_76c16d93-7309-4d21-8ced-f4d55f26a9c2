<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CanSell - Registration with Verification Code</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 24px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        .toast.success { background-color: #10b981; }
        .toast.error { background-color: #ef4444; }
        .toast.info { background-color: #3b82f6; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <div class="text-center mb-8">
            <div class="flex items-center justify-center mb-4">
                <svg class="h-8 w-8 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
                <span class="text-2xl font-bold text-gray-900">CanSell</span>
            </div>
            <h1 class="text-2xl font-bold text-gray-900">Create your account</h1>
            <p class="text-gray-600">Join our marketplace community today</p>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="mb-6">
                <h2 class="text-xl font-semibold text-gray-900">Sign Up</h2>
                <p class="text-gray-600">Create your account to start buying and selling</p>
            </div>

            <form id="registrationForm" class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                        <input type="text" id="firstName" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="John">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                        <input type="text" id="lastName" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Doe">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input type="email" id="email" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="<EMAIL>">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <input type="password" id="password" required minlength="8"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="Create a strong password">
                    <p class="text-xs text-gray-500 mt-1">Must be at least 8 characters</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Confirm Password</label>
                    <input type="password" id="confirmPassword" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="Confirm your password">
                </div>

                <!-- Verification Code Section -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Verification Code</label>
                    <div class="space-y-2">
                        <div class="flex gap-2">
                            <input type="text" id="verificationCode" required pattern="[0-9]{6}" maxlength="6"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="Enter 6-digit code">
                            <button type="button" id="generateCodeBtn" 
                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 whitespace-nowrap">
                                Get Code
                            </button>
                        </div>
                        <div id="codeDisplay" class="hidden p-3 bg-green-50 border border-green-200 rounded-md">
                            <p class="text-sm text-green-800">
                                <strong>Development Mode:</strong> Your verification code is: <code id="generatedCode" class="font-mono font-bold"></code>
                            </p>
                            <p class="text-xs text-green-600 mt-1">
                                In production, this would be sent via email/SMS
                            </p>
                        </div>
                    </div>
                </div>

                <div class="space-y-3">
                    <label class="flex items-start space-x-2">
                        <input type="checkbox" id="agreeToTerms" required class="mt-1">
                        <span class="text-sm text-gray-700">
                            I agree to the <a href="#" class="text-blue-600 hover:underline">Terms of Service</a> 
                            and <a href="#" class="text-blue-600 hover:underline">Privacy Policy</a>
                        </span>
                    </label>
                    
                    <label class="flex items-start space-x-2">
                        <input type="checkbox" id="marketingEmails" class="mt-1">
                        <span class="text-sm text-gray-700">
                            I want to receive marketing emails about new features and promotions
                        </span>
                    </label>
                </div>

                <button type="submit" id="submitBtn" 
                        class="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium">
                    Create Account
                </button>
            </form>

            <p class="text-center text-sm text-gray-600 mt-6">
                Already have an account? 
                <a href="#" class="text-blue-600 hover:underline">Sign in</a>
            </p>
        </div>
    </div>

    <script>
        const BACKEND_URL = 'http://localhost:3001';
        
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        document.getElementById('generateCodeBtn').addEventListener('click', async function() {
            const email = document.getElementById('email').value;
            if (!email) {
                showToast('Please enter your email first', 'error');
                return;
            }

            const btn = this;
            btn.disabled = true;
            btn.textContent = 'Generating...';

            try {
                const response = await fetch(`${BACKEND_URL}/auth/generate-verification-code`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        type: 'registration'
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    document.getElementById('generatedCode').textContent = data.code;
                    document.getElementById('verificationCode').value = data.code; // Auto-fill for demo
                    document.getElementById('codeDisplay').classList.remove('hidden');
                    showToast('Verification code generated! (Auto-filled for development)', 'success');
                } else {
                    showToast(data.message || 'Failed to generate verification code', 'error');
                }
            } catch (error) {
                showToast('Failed to generate verification code', 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = 'Get Code';
            }
        });

        document.getElementById('registrationForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            // Validate passwords match
            if (data.password !== data.confirmPassword) {
                showToast('Passwords do not match', 'error');
                return;
            }

            // Validate terms agreement
            if (!document.getElementById('agreeToTerms').checked) {
                showToast('Please agree to the Terms of Service and Privacy Policy', 'error');
                return;
            }

            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.textContent = 'Creating Account...';

            try {
                const response = await fetch(`${BACKEND_URL}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        firstName: data.firstName,
                        lastName: data.lastName,
                        email: data.email,
                        password: data.password,
                        verificationCode: data.verificationCode
                    })
                });

                const result = await response.json();

                if (response.ok) {
                    showToast('Account created successfully!', 'success');
                    // In a real app, redirect to login or dashboard
                    setTimeout(() => {
                        showToast('Redirecting to login...', 'info');
                    }, 2000);
                } else {
                    showToast(result.message || 'Failed to create account', 'error');
                }
            } catch (error) {
                showToast('Failed to create account', 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Create Account';
            }
        });
    </script>
</body>
</html>
