hoistPattern:
  - '*'
hoistedDependencies:
  /@alloc/quick-lru/5.2.0:
    '@alloc/quick-lru': private
  /@ampproject/remapping/2.3.0:
    '@ampproject/remapping': private
  /@angular-devkit/core/17.3.11(chokidar@3.6.0):
    '@angular-devkit/core': private
  /@angular-devkit/schematics-cli/17.3.11(chokidar@3.6.0):
    '@angular-devkit/schematics-cli': private
  /@angular-devkit/schematics/17.3.11(chokidar@3.6.0):
    '@angular-devkit/schematics': private
  /@babel/code-frame/7.27.1:
    '@babel/code-frame': private
  /@babel/compat-data/7.28.0:
    '@babel/compat-data': private
  /@babel/core/7.28.0:
    '@babel/core': private
  /@babel/generator/7.28.0:
    '@babel/generator': private
  /@babel/helper-compilation-targets/7.27.2:
    '@babel/helper-compilation-targets': private
  /@babel/helper-globals/7.28.0:
    '@babel/helper-globals': private
  /@babel/helper-module-imports/7.27.1:
    '@babel/helper-module-imports': private
  /@babel/helper-module-transforms/7.27.3(@babel/core@7.28.0):
    '@babel/helper-module-transforms': private
  /@babel/helper-plugin-utils/7.27.1:
    '@babel/helper-plugin-utils': private
  /@babel/helper-string-parser/7.27.1:
    '@babel/helper-string-parser': private
  /@babel/helper-validator-identifier/7.27.1:
    '@babel/helper-validator-identifier': private
  /@babel/helper-validator-option/7.27.1:
    '@babel/helper-validator-option': private
  /@babel/helpers/7.28.2:
    '@babel/helpers': private
  /@babel/parser/7.28.0:
    '@babel/parser': private
  /@babel/plugin-syntax-async-generators/7.8.4(@babel/core@7.28.0):
    '@babel/plugin-syntax-async-generators': private
  /@babel/plugin-syntax-bigint/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-bigint': private
  /@babel/plugin-syntax-class-properties/7.12.13(@babel/core@7.28.0):
    '@babel/plugin-syntax-class-properties': private
  /@babel/plugin-syntax-class-static-block/7.14.5(@babel/core@7.28.0):
    '@babel/plugin-syntax-class-static-block': private
  /@babel/plugin-syntax-import-attributes/7.27.1(@babel/core@7.28.0):
    '@babel/plugin-syntax-import-attributes': private
  /@babel/plugin-syntax-import-meta/7.10.4(@babel/core@7.28.0):
    '@babel/plugin-syntax-import-meta': private
  /@babel/plugin-syntax-json-strings/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-json-strings': private
  /@babel/plugin-syntax-jsx/7.27.1(@babel/core@7.28.0):
    '@babel/plugin-syntax-jsx': private
  /@babel/plugin-syntax-logical-assignment-operators/7.10.4(@babel/core@7.28.0):
    '@babel/plugin-syntax-logical-assignment-operators': private
  /@babel/plugin-syntax-nullish-coalescing-operator/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  /@babel/plugin-syntax-numeric-separator/7.10.4(@babel/core@7.28.0):
    '@babel/plugin-syntax-numeric-separator': private
  /@babel/plugin-syntax-object-rest-spread/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-object-rest-spread': private
  /@babel/plugin-syntax-optional-catch-binding/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-optional-catch-binding': private
  /@babel/plugin-syntax-optional-chaining/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-optional-chaining': private
  /@babel/plugin-syntax-private-property-in-object/7.14.5(@babel/core@7.28.0):
    '@babel/plugin-syntax-private-property-in-object': private
  /@babel/plugin-syntax-top-level-await/7.14.5(@babel/core@7.28.0):
    '@babel/plugin-syntax-top-level-await': private
  /@babel/plugin-syntax-typescript/7.27.1(@babel/core@7.28.0):
    '@babel/plugin-syntax-typescript': private
  /@babel/runtime-corejs3/7.28.2:
    '@babel/runtime-corejs3': private
  /@babel/runtime/7.28.2:
    '@babel/runtime': private
  /@babel/template/7.27.2:
    '@babel/template': private
  /@babel/traverse/7.28.0:
    '@babel/traverse': private
  /@babel/types/7.28.2:
    '@babel/types': private
  /@bcoe/v8-coverage/0.2.3:
    '@bcoe/v8-coverage': private
  /@borewit/text-codec/0.1.1:
    '@borewit/text-codec': private
  /@colors/colors/1.5.0:
    '@colors/colors': private
  /@cspotcode/source-map-support/0.8.1:
    '@cspotcode/source-map-support': private
  /@eslint-community/eslint-utils/4.7.0(eslint@8.57.1):
    '@eslint-community/eslint-utils': public
  /@eslint-community/regexpp/4.12.1:
    '@eslint-community/regexpp': public
  /@eslint/eslintrc/2.1.4:
    '@eslint/eslintrc': public
  /@eslint/js/8.57.1:
    '@eslint/js': public
  /@floating-ui/core/1.7.3:
    '@floating-ui/core': private
  /@floating-ui/dom/1.7.3:
    '@floating-ui/dom': private
  /@floating-ui/react-dom/2.1.5(react-dom@18.3.1)(react@18.3.1):
    '@floating-ui/react-dom': private
  /@floating-ui/utils/0.2.10:
    '@floating-ui/utils': private
  /@hookform/resolvers/3.10.0(react-hook-form@7.62.0):
    '@hookform/resolvers': private
  /@humanwhocodes/config-array/0.13.0:
    '@humanwhocodes/config-array': private
  /@humanwhocodes/module-importer/1.0.1:
    '@humanwhocodes/module-importer': private
  /@humanwhocodes/object-schema/2.0.3:
    '@humanwhocodes/object-schema': private
  /@img/sharp-darwin-arm64/0.34.3:
    '@img/sharp-darwin-arm64': private
  /@img/sharp-darwin-x64/0.34.3:
    '@img/sharp-darwin-x64': private
  /@img/sharp-libvips-darwin-arm64/1.2.0:
    '@img/sharp-libvips-darwin-arm64': private
  /@img/sharp-libvips-darwin-x64/1.2.0:
    '@img/sharp-libvips-darwin-x64': private
  /@img/sharp-libvips-linux-arm/1.2.0:
    '@img/sharp-libvips-linux-arm': private
  /@img/sharp-libvips-linux-arm64/1.2.0:
    '@img/sharp-libvips-linux-arm64': private
  /@img/sharp-libvips-linux-ppc64/1.2.0:
    '@img/sharp-libvips-linux-ppc64': private
  /@img/sharp-libvips-linux-s390x/1.2.0:
    '@img/sharp-libvips-linux-s390x': private
  /@img/sharp-libvips-linux-x64/1.2.0:
    '@img/sharp-libvips-linux-x64': private
  /@img/sharp-libvips-linuxmusl-arm64/1.2.0:
    '@img/sharp-libvips-linuxmusl-arm64': private
  /@img/sharp-libvips-linuxmusl-x64/1.2.0:
    '@img/sharp-libvips-linuxmusl-x64': private
  /@img/sharp-linux-arm/0.34.3:
    '@img/sharp-linux-arm': private
  /@img/sharp-linux-arm64/0.34.3:
    '@img/sharp-linux-arm64': private
  /@img/sharp-linux-ppc64/0.34.3:
    '@img/sharp-linux-ppc64': private
  /@img/sharp-linux-s390x/0.34.3:
    '@img/sharp-linux-s390x': private
  /@img/sharp-linux-x64/0.34.3:
    '@img/sharp-linux-x64': private
  /@img/sharp-linuxmusl-arm64/0.34.3:
    '@img/sharp-linuxmusl-arm64': private
  /@img/sharp-linuxmusl-x64/0.34.3:
    '@img/sharp-linuxmusl-x64': private
  /@img/sharp-wasm32/0.34.3:
    '@img/sharp-wasm32': private
  /@img/sharp-win32-arm64/0.34.3:
    '@img/sharp-win32-arm64': private
  /@img/sharp-win32-ia32/0.34.3:
    '@img/sharp-win32-ia32': private
  /@img/sharp-win32-x64/0.34.3:
    '@img/sharp-win32-x64': private
  /@inquirer/external-editor/1.0.1(@types/node@20.19.10):
    '@inquirer/external-editor': private
  /@isaacs/cliui/8.0.2:
    '@isaacs/cliui': private
  /@istanbuljs/load-nyc-config/1.1.0:
    '@istanbuljs/load-nyc-config': private
  /@istanbuljs/schema/0.1.3:
    '@istanbuljs/schema': private
  /@jest/console/29.7.0:
    '@jest/console': private
  /@jest/core/29.7.0(ts-node@10.9.2):
    '@jest/core': private
  /@jest/environment/29.7.0:
    '@jest/environment': private
  /@jest/expect-utils/29.7.0:
    '@jest/expect-utils': private
  /@jest/expect/29.7.0:
    '@jest/expect': private
  /@jest/fake-timers/29.7.0:
    '@jest/fake-timers': private
  /@jest/globals/29.7.0:
    '@jest/globals': private
  /@jest/reporters/29.7.0:
    '@jest/reporters': private
  /@jest/schemas/29.6.3:
    '@jest/schemas': private
  /@jest/source-map/29.6.3:
    '@jest/source-map': private
  /@jest/test-result/29.7.0:
    '@jest/test-result': private
  /@jest/test-sequencer/29.7.0:
    '@jest/test-sequencer': private
  /@jest/transform/29.7.0:
    '@jest/transform': private
  /@jest/types/29.6.3:
    '@jest/types': private
  /@jridgewell/gen-mapping/0.3.13:
    '@jridgewell/gen-mapping': private
  /@jridgewell/resolve-uri/3.1.2:
    '@jridgewell/resolve-uri': private
  /@jridgewell/source-map/0.3.11:
    '@jridgewell/source-map': private
  /@jridgewell/sourcemap-codec/1.5.5:
    '@jridgewell/sourcemap-codec': private
  /@jridgewell/trace-mapping/0.3.9:
    '@jridgewell/trace-mapping': private
  /@ljharb/through/2.3.14:
    '@ljharb/through': private
  /@lukeed/csprng/1.1.0:
    '@lukeed/csprng': private
  /@microsoft/tsdoc/0.15.1:
    '@microsoft/tsdoc': private
  /@mongodb-js/saslprep/1.3.0:
    '@mongodb-js/saslprep': private
  /@nestjs/cli/10.4.9:
    '@nestjs/cli': private
  /@nestjs/common/10.4.20(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2):
    '@nestjs/common': private
  /@nestjs/config/3.3.0(@nestjs/common@10.4.20)(rxjs@7.8.2):
    '@nestjs/config': private
  /@nestjs/core/10.4.20(@nestjs/common@10.4.20)(@nestjs/microservices@10.4.20)(@nestjs/platform-express@10.4.20)(reflect-metadata@0.2.2)(rxjs@7.8.2):
    '@nestjs/core': private
  /@nestjs/jwt/10.2.0(@nestjs/common@10.4.20):
    '@nestjs/jwt': private
  /@nestjs/mapped-types/2.0.5(@nestjs/common@10.4.20)(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2):
    '@nestjs/mapped-types': private
  /@nestjs/microservices/10.4.20(@nestjs/common@10.4.20)(@nestjs/core@10.4.20)(kafkajs@2.2.4)(reflect-metadata@0.2.2)(rxjs@7.8.2):
    '@nestjs/microservices': private
  /@nestjs/mongoose/10.1.0(@nestjs/common@10.4.20)(@nestjs/core@10.4.20)(mongoose@8.17.1)(rxjs@7.8.2):
    '@nestjs/mongoose': private
  /@nestjs/passport/10.0.3(@nestjs/common@10.4.20)(passport@0.7.0):
    '@nestjs/passport': private
  /@nestjs/platform-express/10.4.20(@nestjs/common@10.4.20)(@nestjs/core@10.4.20):
    '@nestjs/platform-express': private
  /@nestjs/schematics/10.2.3(typescript@5.9.2):
    '@nestjs/schematics': private
  /@nestjs/swagger/7.4.2(@nestjs/common@10.4.20)(@nestjs/core@10.4.20)(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2):
    '@nestjs/swagger': private
  /@nestjs/testing/10.4.20(@nestjs/common@10.4.20)(@nestjs/core@10.4.20)(@nestjs/microservices@10.4.20)(@nestjs/platform-express@10.4.20):
    '@nestjs/testing': private
  /@nestjs/throttler/5.2.0(@nestjs/common@10.4.20)(@nestjs/core@10.4.20)(reflect-metadata@0.2.2):
    '@nestjs/throttler': private
  /@next/env/15.4.6:
    '@next/env': private
  /@next/eslint-plugin-next/15.4.6:
    '@next/eslint-plugin-next': public
  /@next/swc-darwin-arm64/15.4.6:
    '@next/swc-darwin-arm64': private
  /@next/swc-darwin-x64/15.4.6:
    '@next/swc-darwin-x64': private
  /@next/swc-linux-arm64-gnu/15.4.6:
    '@next/swc-linux-arm64-gnu': private
  /@next/swc-linux-arm64-musl/15.4.6:
    '@next/swc-linux-arm64-musl': private
  /@next/swc-linux-x64-gnu/15.4.6:
    '@next/swc-linux-x64-gnu': private
  /@next/swc-linux-x64-musl/15.4.6:
    '@next/swc-linux-x64-musl': private
  /@next/swc-win32-arm64-msvc/15.4.6:
    '@next/swc-win32-arm64-msvc': private
  /@next/swc-win32-x64-msvc/15.4.6:
    '@next/swc-win32-x64-msvc': private
  /@noble/hashes/1.8.0:
    '@noble/hashes': private
  /@nodelib/fs.scandir/2.1.5:
    '@nodelib/fs.scandir': private
  /@nodelib/fs.stat/2.0.5:
    '@nodelib/fs.stat': private
  /@nodelib/fs.walk/1.2.8:
    '@nodelib/fs.walk': private
  /@nolyfill/is-core-module/1.0.39:
    '@nolyfill/is-core-module': private
  /@nuxtjs/opencollective/0.3.2:
    '@nuxtjs/opencollective': private
  /@panva/hkdf/1.2.1:
    '@panva/hkdf': private
  /@paralleldrive/cuid2/2.2.2:
    '@paralleldrive/cuid2': private
  /@pkgjs/parseargs/0.11.0:
    '@pkgjs/parseargs': private
  /@pkgr/core/0.2.9:
    '@pkgr/core': private
  /@prisma/client/5.22.0(prisma@5.22.0):
    '@prisma/client': private
  /@prisma/debug/5.22.0:
    '@prisma/debug': private
  /@prisma/engines-version/5.22.0-44.605197351a3c8bdd595af2d2a9bc3025bca48ea2:
    '@prisma/engines-version': private
  /@prisma/engines/5.22.0:
    '@prisma/engines': private
  /@prisma/fetch-engine/5.22.0:
    '@prisma/fetch-engine': private
  /@prisma/get-platform/5.22.0:
    '@prisma/get-platform': private
  /@radix-ui/number/1.1.1:
    '@radix-ui/number': private
  /@radix-ui/primitive/1.1.3:
    '@radix-ui/primitive': private
  /@radix-ui/react-arrow/1.1.7(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-arrow': private
  /@radix-ui/react-avatar/1.1.10(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-avatar': private
  /@radix-ui/react-collection/1.1.7(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-collection': private
  /@radix-ui/react-compose-refs/1.1.2(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-compose-refs': private
  /@radix-ui/react-context/1.1.2(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-context': private
  /@radix-ui/react-dialog/1.1.15(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-dialog': private
  /@radix-ui/react-direction/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-direction': private
  /@radix-ui/react-dismissable-layer/1.1.11(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-dismissable-layer': private
  /@radix-ui/react-dropdown-menu/2.1.16(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-dropdown-menu': private
  /@radix-ui/react-focus-guards/1.1.3(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-focus-guards': private
  /@radix-ui/react-focus-scope/1.1.7(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-focus-scope': private
  /@radix-ui/react-id/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-id': private
  /@radix-ui/react-label/2.1.7(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-label': private
  /@radix-ui/react-menu/2.1.16(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-menu': private
  /@radix-ui/react-navigation-menu/1.2.14(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-navigation-menu': private
  /@radix-ui/react-popper/1.2.8(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-popper': private
  /@radix-ui/react-portal/1.1.9(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-portal': private
  /@radix-ui/react-presence/1.1.5(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-presence': private
  /@radix-ui/react-primitive/2.1.3(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-primitive': private
  /@radix-ui/react-roving-focus/1.1.11(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-roving-focus': private
  /@radix-ui/react-select/2.2.6(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-select': private
  /@radix-ui/react-separator/1.1.7(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-separator': private
  /@radix-ui/react-slot/1.2.3(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-slot': private
  /@radix-ui/react-tabs/1.1.13(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-tabs': private
  /@radix-ui/react-toast/1.2.15(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-toast': private
  /@radix-ui/react-use-callback-ref/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-callback-ref': private
  /@radix-ui/react-use-controllable-state/1.2.2(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-controllable-state': private
  /@radix-ui/react-use-effect-event/0.0.2(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-effect-event': private
  /@radix-ui/react-use-escape-keydown/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-escape-keydown': private
  /@radix-ui/react-use-is-hydrated/0.1.0(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-is-hydrated': private
  /@radix-ui/react-use-layout-effect/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-layout-effect': private
  /@radix-ui/react-use-previous/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-previous': private
  /@radix-ui/react-use-rect/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-rect': private
  /@radix-ui/react-use-size/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-size': private
  /@radix-ui/react-visually-hidden/1.2.3(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-visually-hidden': private
  /@radix-ui/rect/1.1.1:
    '@radix-ui/rect': private
  /@redis/bloom/1.2.0(@redis/client@1.6.1):
    '@redis/bloom': private
  /@redis/client/1.6.1:
    '@redis/client': private
  /@redis/graph/1.1.1(@redis/client@1.6.1):
    '@redis/graph': private
  /@redis/json/1.0.7(@redis/client@1.6.1):
    '@redis/json': private
  /@redis/search/1.2.0(@redis/client@1.6.1):
    '@redis/search': private
  /@redis/time-series/1.1.0(@redis/client@1.6.1):
    '@redis/time-series': private
  /@rtsao/scc/1.1.0:
    '@rtsao/scc': private
  /@rushstack/eslint-patch/1.12.0:
    '@rushstack/eslint-patch': public
  /@sinclair/typebox/0.27.8:
    '@sinclair/typebox': private
  /@sinonjs/commons/3.0.1:
    '@sinonjs/commons': private
  /@sinonjs/fake-timers/10.3.0:
    '@sinonjs/fake-timers': private
  /@swc/helpers/0.5.15:
    '@swc/helpers': private
  /@tailwindcss/forms/0.5.10(tailwindcss@3.4.17):
    '@tailwindcss/forms': private
  /@tailwindcss/typography/0.5.16(tailwindcss@3.4.17):
    '@tailwindcss/typography': private
  /@tanstack/query-core/5.83.1:
    '@tanstack/query-core': private
  /@tanstack/react-query/5.85.0(react@18.3.1):
    '@tanstack/react-query': private
  /@tokenizer/inflate/0.2.7:
    '@tokenizer/inflate': private
  /@tokenizer/token/0.3.0:
    '@tokenizer/token': private
  /@tootallnate/quickjs-emscripten/0.23.0:
    '@tootallnate/quickjs-emscripten': private
  /@tsconfig/node10/1.0.11:
    '@tsconfig/node10': private
  /@tsconfig/node12/1.0.11:
    '@tsconfig/node12': private
  /@tsconfig/node14/1.0.3:
    '@tsconfig/node14': private
  /@tsconfig/node16/1.0.4:
    '@tsconfig/node16': private
  /@turbo/workspaces/2.5.5(@types/node@20.19.10):
    '@turbo/workspaces': private
  /@types/babel__core/7.20.5:
    '@types/babel__core': private
  /@types/babel__generator/7.27.0:
    '@types/babel__generator': private
  /@types/babel__template/7.4.4:
    '@types/babel__template': private
  /@types/babel__traverse/7.28.0:
    '@types/babel__traverse': private
  /@types/bcryptjs/2.4.6:
    '@types/bcryptjs': private
  /@types/body-parser/1.19.6:
    '@types/body-parser': private
  /@types/connect/3.4.38:
    '@types/connect': private
  /@types/cookiejar/2.1.5:
    '@types/cookiejar': private
  /@types/eslint-scope/3.7.7:
    '@types/eslint-scope': public
  /@types/eslint/9.6.1:
    '@types/eslint': public
  /@types/estree/1.0.8:
    '@types/estree': private
  /@types/express-serve-static-core/5.0.7:
    '@types/express-serve-static-core': private
  /@types/express/5.0.3:
    '@types/express': private
  /@types/glob/7.2.0:
    '@types/glob': private
  /@types/graceful-fs/4.1.9:
    '@types/graceful-fs': private
  /@types/http-errors/2.0.5:
    '@types/http-errors': private
  /@types/inquirer/6.5.0:
    '@types/inquirer': private
  /@types/istanbul-lib-coverage/2.0.6:
    '@types/istanbul-lib-coverage': private
  /@types/istanbul-lib-report/3.0.3:
    '@types/istanbul-lib-report': private
  /@types/istanbul-reports/3.0.4:
    '@types/istanbul-reports': private
  /@types/jest/29.5.14:
    '@types/jest': private
  /@types/json-schema/7.0.15:
    '@types/json-schema': private
  /@types/json5/0.0.29:
    '@types/json5': private
  /@types/jsonwebtoken/9.0.5:
    '@types/jsonwebtoken': private
  /@types/methods/1.1.4:
    '@types/methods': private
  /@types/mime/1.3.5:
    '@types/mime': private
  /@types/minimatch/6.0.0:
    '@types/minimatch': private
  /@types/ms/2.1.0:
    '@types/ms': private
  /@types/node/20.19.10:
    '@types/node': private
  /@types/passport-jwt/3.0.13:
    '@types/passport-jwt': private
  /@types/passport-local/1.0.38:
    '@types/passport-local': private
  /@types/passport-strategy/0.2.38:
    '@types/passport-strategy': private
  /@types/passport/1.0.17:
    '@types/passport': private
  /@types/prop-types/15.7.15:
    '@types/prop-types': private
  /@types/qs/6.14.0:
    '@types/qs': private
  /@types/range-parser/1.2.7:
    '@types/range-parser': private
  /@types/react-dom/18.3.7(@types/react@18.3.23):
    '@types/react-dom': private
  /@types/react/18.3.23:
    '@types/react': private
  /@types/semver/7.7.0:
    '@types/semver': private
  /@types/send/0.17.5:
    '@types/send': private
  /@types/serve-static/1.15.8:
    '@types/serve-static': private
  /@types/stack-utils/2.0.3:
    '@types/stack-utils': private
  /@types/superagent/8.1.9:
    '@types/superagent': private
  /@types/supertest/6.0.3:
    '@types/supertest': private
  /@types/through/0.0.33:
    '@types/through': private
  /@types/tinycolor2/1.4.6:
    '@types/tinycolor2': private
  /@types/validator/13.15.2:
    '@types/validator': private
  /@types/webidl-conversions/7.0.3:
    '@types/webidl-conversions': private
  /@types/whatwg-url/11.0.5:
    '@types/whatwg-url': private
  /@types/yargs-parser/21.0.3:
    '@types/yargs-parser': private
  /@types/yargs/17.0.33:
    '@types/yargs': private
  /@typescript-eslint/eslint-plugin/8.39.1(@typescript-eslint/parser@8.39.1)(eslint@8.57.1)(typescript@5.9.2):
    '@typescript-eslint/eslint-plugin': public
  /@typescript-eslint/parser/8.39.1(eslint@8.57.1)(typescript@5.9.2):
    '@typescript-eslint/parser': public
  /@typescript-eslint/project-service/8.39.1(typescript@5.9.2):
    '@typescript-eslint/project-service': public
  /@typescript-eslint/scope-manager/6.21.0:
    '@typescript-eslint/scope-manager': public
  /@typescript-eslint/tsconfig-utils/8.39.1(typescript@5.9.2):
    '@typescript-eslint/tsconfig-utils': public
  /@typescript-eslint/type-utils/6.21.0(eslint@8.57.1)(typescript@5.9.2):
    '@typescript-eslint/type-utils': public
  /@typescript-eslint/types/6.21.0:
    '@typescript-eslint/types': public
  /@typescript-eslint/typescript-estree/6.21.0(typescript@5.9.2):
    '@typescript-eslint/typescript-estree': public
  /@typescript-eslint/utils/6.21.0(eslint@8.57.1)(typescript@5.9.2):
    '@typescript-eslint/utils': public
  /@typescript-eslint/visitor-keys/6.21.0:
    '@typescript-eslint/visitor-keys': public
  /@ungap/structured-clone/1.3.0:
    '@ungap/structured-clone': private
  /@unrs/resolver-binding-android-arm-eabi/1.11.1:
    '@unrs/resolver-binding-android-arm-eabi': private
  /@unrs/resolver-binding-android-arm64/1.11.1:
    '@unrs/resolver-binding-android-arm64': private
  /@unrs/resolver-binding-darwin-arm64/1.11.1:
    '@unrs/resolver-binding-darwin-arm64': private
  /@unrs/resolver-binding-darwin-x64/1.11.1:
    '@unrs/resolver-binding-darwin-x64': private
  /@unrs/resolver-binding-freebsd-x64/1.11.1:
    '@unrs/resolver-binding-freebsd-x64': private
  /@unrs/resolver-binding-linux-arm-gnueabihf/1.11.1:
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  /@unrs/resolver-binding-linux-arm-musleabihf/1.11.1:
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  /@unrs/resolver-binding-linux-arm64-gnu/1.11.1:
    '@unrs/resolver-binding-linux-arm64-gnu': private
  /@unrs/resolver-binding-linux-arm64-musl/1.11.1:
    '@unrs/resolver-binding-linux-arm64-musl': private
  /@unrs/resolver-binding-linux-ppc64-gnu/1.11.1:
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  /@unrs/resolver-binding-linux-riscv64-gnu/1.11.1:
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  /@unrs/resolver-binding-linux-riscv64-musl/1.11.1:
    '@unrs/resolver-binding-linux-riscv64-musl': private
  /@unrs/resolver-binding-linux-s390x-gnu/1.11.1:
    '@unrs/resolver-binding-linux-s390x-gnu': private
  /@unrs/resolver-binding-linux-x64-gnu/1.11.1:
    '@unrs/resolver-binding-linux-x64-gnu': private
  /@unrs/resolver-binding-linux-x64-musl/1.11.1:
    '@unrs/resolver-binding-linux-x64-musl': private
  /@unrs/resolver-binding-wasm32-wasi/1.11.1:
    '@unrs/resolver-binding-wasm32-wasi': private
  /@unrs/resolver-binding-win32-arm64-msvc/1.11.1:
    '@unrs/resolver-binding-win32-arm64-msvc': private
  /@unrs/resolver-binding-win32-ia32-msvc/1.11.1:
    '@unrs/resolver-binding-win32-ia32-msvc': private
  /@unrs/resolver-binding-win32-x64-msvc/1.11.1:
    '@unrs/resolver-binding-win32-x64-msvc': private
  /@webassemblyjs/ast/1.14.1:
    '@webassemblyjs/ast': private
  /@webassemblyjs/floating-point-hex-parser/1.13.2:
    '@webassemblyjs/floating-point-hex-parser': private
  /@webassemblyjs/helper-api-error/1.13.2:
    '@webassemblyjs/helper-api-error': private
  /@webassemblyjs/helper-buffer/1.14.1:
    '@webassemblyjs/helper-buffer': private
  /@webassemblyjs/helper-numbers/1.13.2:
    '@webassemblyjs/helper-numbers': private
  /@webassemblyjs/helper-wasm-bytecode/1.13.2:
    '@webassemblyjs/helper-wasm-bytecode': private
  /@webassemblyjs/helper-wasm-section/1.14.1:
    '@webassemblyjs/helper-wasm-section': private
  /@webassemblyjs/ieee754/1.13.2:
    '@webassemblyjs/ieee754': private
  /@webassemblyjs/leb128/1.13.2:
    '@webassemblyjs/leb128': private
  /@webassemblyjs/utf8/1.13.2:
    '@webassemblyjs/utf8': private
  /@webassemblyjs/wasm-edit/1.14.1:
    '@webassemblyjs/wasm-edit': private
  /@webassemblyjs/wasm-gen/1.14.1:
    '@webassemblyjs/wasm-gen': private
  /@webassemblyjs/wasm-opt/1.14.1:
    '@webassemblyjs/wasm-opt': private
  /@webassemblyjs/wasm-parser/1.14.1:
    '@webassemblyjs/wasm-parser': private
  /@webassemblyjs/wast-printer/1.14.1:
    '@webassemblyjs/wast-printer': private
  /@xtuc/ieee754/1.2.0:
    '@xtuc/ieee754': private
  /@xtuc/long/4.2.2:
    '@xtuc/long': private
  /accepts/1.3.8:
    accepts: private
  /acorn-import-phases/1.0.4(acorn@8.15.0):
    acorn-import-phases: private
  /acorn-jsx/5.3.2(acorn@8.15.0):
    acorn-jsx: private
  /acorn-walk/8.3.4:
    acorn-walk: private
  /acorn/8.15.0:
    acorn: private
  /agent-base/7.1.4:
    agent-base: private
  /aggregate-error/3.1.0:
    aggregate-error: private
  /ajv-formats/2.1.1(ajv@8.12.0):
    ajv-formats: private
  /ajv-keywords/3.5.2(ajv@6.12.6):
    ajv-keywords: private
  /ajv/6.12.6:
    ajv: private
  /ansi-colors/4.1.3:
    ansi-colors: private
  /ansi-escapes/4.3.2:
    ansi-escapes: private
  /ansi-regex/5.0.1:
    ansi-regex: private
  /ansi-styles/4.3.0:
    ansi-styles: private
  /any-promise/1.3.0:
    any-promise: private
  /anymatch/3.1.3:
    anymatch: private
  /append-field/1.0.0:
    append-field: private
  /arg/5.0.2:
    arg: private
  /argparse/2.0.1:
    argparse: private
  /aria-hidden/1.2.6:
    aria-hidden: private
  /aria-query/5.3.2:
    aria-query: private
  /array-buffer-byte-length/1.0.2:
    array-buffer-byte-length: private
  /array-flatten/1.1.1:
    array-flatten: private
  /array-includes/3.1.9:
    array-includes: private
  /array-timsort/1.0.3:
    array-timsort: private
  /array-union/2.1.0:
    array-union: private
  /array.prototype.findlast/1.2.5:
    array.prototype.findlast: private
  /array.prototype.findlastindex/1.2.6:
    array.prototype.findlastindex: private
  /array.prototype.flat/1.3.3:
    array.prototype.flat: private
  /array.prototype.flatmap/1.3.3:
    array.prototype.flatmap: private
  /array.prototype.tosorted/1.1.4:
    array.prototype.tosorted: private
  /arraybuffer.prototype.slice/1.0.4:
    arraybuffer.prototype.slice: private
  /asap/2.0.6:
    asap: private
  /ast-types-flow/0.0.8:
    ast-types-flow: private
  /ast-types/0.13.4:
    ast-types: private
  /async-function/1.0.0:
    async-function: private
  /asynckit/0.4.0:
    asynckit: private
  /autoprefixer/10.4.21(postcss@8.5.6):
    autoprefixer: private
  /available-typed-arrays/1.0.7:
    available-typed-arrays: private
  /axe-core/4.10.3:
    axe-core: private
  /axobject-query/4.1.0:
    axobject-query: private
  /babel-jest/29.7.0(@babel/core@7.28.0):
    babel-jest: private
  /babel-plugin-istanbul/6.1.1:
    babel-plugin-istanbul: private
  /babel-plugin-jest-hoist/29.6.3:
    babel-plugin-jest-hoist: private
  /babel-preset-current-node-syntax/1.2.0(@babel/core@7.28.0):
    babel-preset-current-node-syntax: private
  /babel-preset-jest/29.6.3(@babel/core@7.28.0):
    babel-preset-jest: private
  /balanced-match/1.0.2:
    balanced-match: private
  /base64-js/1.5.1:
    base64-js: private
  /basic-ftp/5.0.5:
    basic-ftp: private
  /bcryptjs/2.4.3:
    bcryptjs: private
  /binary-extensions/2.3.0:
    binary-extensions: private
  /bl/4.1.0:
    bl: private
  /body-parser/1.20.3:
    body-parser: private
  /brace-expansion/1.1.12:
    brace-expansion: private
  /braces/3.0.3:
    braces: private
  /browserslist/4.25.2:
    browserslist: private
  /bs-logger/0.2.6:
    bs-logger: private
  /bser/2.1.1:
    bser: private
  /bson/6.10.4:
    bson: private
  /buffer-equal-constant-time/1.0.1:
    buffer-equal-constant-time: private
  /buffer-from/1.1.2:
    buffer-from: private
  /buffer/5.7.1:
    buffer: private
  /busboy/1.6.0:
    busboy: private
  /bytes/3.1.2:
    bytes: private
  /call-bind-apply-helpers/1.0.2:
    call-bind-apply-helpers: private
  /call-bind/1.0.8:
    call-bind: private
  /call-bound/1.0.4:
    call-bound: private
  /callsites/3.1.0:
    callsites: private
  /camel-case/3.0.0:
    camel-case: private
  /camelcase-css/2.0.1:
    camelcase-css: private
  /camelcase/6.3.0:
    camelcase: private
  /caniuse-lite/1.0.30001734:
    caniuse-lite: private
  /chalk/4.1.2:
    chalk: private
  /change-case/3.1.0:
    change-case: private
  /char-regex/1.0.2:
    char-regex: private
  /chardet/2.1.0:
    chardet: private
  /chokidar/3.6.0:
    chokidar: private
  /chrome-trace-event/1.0.4:
    chrome-trace-event: private
  /ci-info/3.9.0:
    ci-info: private
  /cjs-module-lexer/1.4.3:
    cjs-module-lexer: private
  /class-transformer/0.5.1:
    class-transformer: private
  /class-validator/0.14.2:
    class-validator: private
  /class-variance-authority/0.7.1:
    class-variance-authority: private
  /clean-stack/2.2.0:
    clean-stack: private
  /cli-cursor/3.1.0:
    cli-cursor: private
  /cli-spinners/2.9.2:
    cli-spinners: private
  /cli-table3/0.6.5:
    cli-table3: private
  /cli-width/3.0.0:
    cli-width: private
  /client-only/0.0.1:
    client-only: private
  /cliui/8.0.1:
    cliui: private
  /clone/1.0.4:
    clone: private
  /clsx/2.1.1:
    clsx: private
  /cluster-key-slot/1.1.2:
    cluster-key-slot: private
  /co/4.6.0:
    co: private
  /collect-v8-coverage/1.0.2:
    collect-v8-coverage: private
  /color-convert/2.0.1:
    color-convert: private
  /color-name/1.1.4:
    color-name: private
  /color-string/1.9.1:
    color-string: private
  /color/4.2.3:
    color: private
  /combined-stream/1.0.8:
    combined-stream: private
  /commander/4.1.1:
    commander: private
  /comment-json/4.2.5:
    comment-json: private
  /component-emitter/1.3.1:
    component-emitter: private
  /concat-map/0.0.1:
    concat-map: private
  /concat-stream/2.0.0:
    concat-stream: private
  /consola/2.15.3:
    consola: private
  /constant-case/2.0.0:
    constant-case: private
  /content-disposition/0.5.4:
    content-disposition: private
  /content-type/1.0.5:
    content-type: private
  /convert-source-map/2.0.0:
    convert-source-map: private
  /cookie-signature/1.0.6:
    cookie-signature: private
  /cookie/0.7.2:
    cookie: private
  /cookiejar/2.1.4:
    cookiejar: private
  /core-js-pure/3.45.0:
    core-js-pure: private
  /core-util-is/1.0.3:
    core-util-is: private
  /cors/2.8.5:
    cors: private
  /cosmiconfig/8.3.6(typescript@5.7.2):
    cosmiconfig: private
  /create-jest/29.7.0(@types/node@20.19.10)(ts-node@10.9.2):
    create-jest: private
  /create-require/1.1.1:
    create-require: private
  /cross-spawn/7.0.6:
    cross-spawn: private
  /cssesc/3.0.0:
    cssesc: private
  /csstype/3.1.3:
    csstype: private
  /damerau-levenshtein/1.0.8:
    damerau-levenshtein: private
  /data-uri-to-buffer/6.0.2:
    data-uri-to-buffer: private
  /data-view-buffer/1.0.2:
    data-view-buffer: private
  /data-view-byte-length/1.0.2:
    data-view-byte-length: private
  /data-view-byte-offset/1.0.1:
    data-view-byte-offset: private
  /debug/4.4.1:
    debug: private
  /dedent/1.6.0:
    dedent: private
  /deep-extend/0.6.0:
    deep-extend: private
  /deep-is/0.1.4:
    deep-is: private
  /deepmerge/4.3.1:
    deepmerge: private
  /defaults/1.0.4:
    defaults: private
  /define-data-property/1.1.4:
    define-data-property: private
  /define-properties/1.2.1:
    define-properties: private
  /degenerator/5.0.1:
    degenerator: private
  /del/5.1.0:
    del: private
  /delayed-stream/1.0.0:
    delayed-stream: private
  /depd/2.0.0:
    depd: private
  /destroy/1.2.0:
    destroy: private
  /detect-libc/2.0.4:
    detect-libc: private
  /detect-newline/3.1.0:
    detect-newline: private
  /detect-node-es/1.1.0:
    detect-node-es: private
  /dezalgo/1.0.4:
    dezalgo: private
  /didyoumean/1.2.2:
    didyoumean: private
  /diff-sequences/29.6.3:
    diff-sequences: private
  /diff/4.0.2:
    diff: private
  /dir-glob/3.0.1:
    dir-glob: private
  /dlv/1.1.3:
    dlv: private
  /doctrine/2.1.0:
    doctrine: private
  /dot-case/2.1.1:
    dot-case: private
  /dotenv-expand/10.0.0:
    dotenv-expand: private
  /dotenv/16.4.5:
    dotenv: private
  /dunder-proto/1.0.1:
    dunder-proto: private
  /eastasianwidth/0.2.0:
    eastasianwidth: private
  /ecdsa-sig-formatter/1.0.11:
    ecdsa-sig-formatter: private
  /ee-first/1.1.1:
    ee-first: private
  /electron-to-chromium/1.5.200:
    electron-to-chromium: private
  /emittery/0.13.1:
    emittery: private
  /emoji-regex/9.2.2:
    emoji-regex: private
  /encodeurl/2.0.0:
    encodeurl: private
  /enhanced-resolve/5.18.3:
    enhanced-resolve: private
  /error-ex/1.3.2:
    error-ex: private
  /es-abstract/1.24.0:
    es-abstract: private
  /es-define-property/1.0.1:
    es-define-property: private
  /es-errors/1.3.0:
    es-errors: private
  /es-iterator-helpers/1.2.1:
    es-iterator-helpers: private
  /es-module-lexer/1.7.0:
    es-module-lexer: private
  /es-object-atoms/1.1.1:
    es-object-atoms: private
  /es-set-tostringtag/2.1.0:
    es-set-tostringtag: private
  /es-shim-unscopables/1.1.0:
    es-shim-unscopables: private
  /es-to-primitive/1.3.0:
    es-to-primitive: private
  /escalade/3.2.0:
    escalade: private
  /escape-html/1.0.3:
    escape-html: private
  /escape-string-regexp/4.0.0:
    escape-string-regexp: private
  /escodegen/2.1.0:
    escodegen: private
  /eslint-config-next/15.4.6(eslint@8.57.1)(typescript@5.9.2):
    eslint-config-next: public
  /eslint-config-prettier/9.1.2(eslint@8.57.1):
    eslint-config-prettier: public
  /eslint-import-resolver-node/0.3.9:
    eslint-import-resolver-node: public
  /eslint-import-resolver-typescript/3.10.1(eslint-plugin-import@2.32.0)(eslint@8.57.1):
    eslint-import-resolver-typescript: public
  /eslint-module-utils/2.12.1(@typescript-eslint/parser@6.21.0)(eslint-import-resolver-node@0.3.9)(eslint@8.57.1):
    eslint-module-utils: public
  /eslint-plugin-import/2.32.0(@typescript-eslint/parser@6.21.0)(eslint@8.57.1):
    eslint-plugin-import: public
  /eslint-plugin-jsx-a11y/6.10.2(eslint@8.57.1):
    eslint-plugin-jsx-a11y: public
  /eslint-plugin-prettier/5.5.4(eslint-config-prettier@9.1.2)(eslint@8.57.1)(prettier@3.6.2):
    eslint-plugin-prettier: public
  /eslint-plugin-react-hooks/4.6.2(eslint@8.57.1):
    eslint-plugin-react-hooks: public
  /eslint-plugin-react/7.37.5(eslint@8.57.1):
    eslint-plugin-react: public
  /eslint-scope/7.2.2:
    eslint-scope: public
  /eslint-visitor-keys/3.4.3:
    eslint-visitor-keys: public
  /eslint/8.57.1:
    eslint: public
  /espree/9.6.1:
    espree: private
  /esprima/4.0.1:
    esprima: private
  /esquery/1.6.0:
    esquery: private
  /esrecurse/4.3.0:
    esrecurse: private
  /estraverse/5.3.0:
    estraverse: private
  /esutils/2.0.3:
    esutils: private
  /etag/1.8.1:
    etag: private
  /events/3.3.0:
    events: private
  /execa/5.1.1:
    execa: private
  /exit/0.1.2:
    exit: private
  /expect/29.7.0:
    expect: private
  /express/4.21.2:
    express: private
  /external-editor/3.1.0:
    external-editor: private
  /fast-deep-equal/3.1.3:
    fast-deep-equal: private
  /fast-diff/1.3.0:
    fast-diff: private
  /fast-glob/3.3.3:
    fast-glob: private
  /fast-json-stable-stringify/2.1.0:
    fast-json-stable-stringify: private
  /fast-levenshtein/2.0.6:
    fast-levenshtein: private
  /fast-safe-stringify/2.1.1:
    fast-safe-stringify: private
  /fast-uri/3.0.6:
    fast-uri: private
  /fastq/1.19.1:
    fastq: private
  /fb-watchman/2.0.2:
    fb-watchman: private
  /fdir/6.4.6(picomatch@4.0.3):
    fdir: private
  /fflate/0.8.2:
    fflate: private
  /figures/3.2.0:
    figures: private
  /file-entry-cache/6.0.1:
    file-entry-cache: private
  /file-type/20.4.1:
    file-type: private
  /fill-range/7.1.1:
    fill-range: private
  /finalhandler/1.3.1:
    finalhandler: private
  /find-up/5.0.0:
    find-up: private
  /flat-cache/3.2.0:
    flat-cache: private
  /flatted/3.3.3:
    flatted: private
  /for-each/0.3.5:
    for-each: private
  /foreground-child/3.3.1:
    foreground-child: private
  /fork-ts-checker-webpack-plugin/9.0.2(typescript@5.7.2)(webpack@5.97.1):
    fork-ts-checker-webpack-plugin: private
  /form-data/4.0.4:
    form-data: private
  /formidable/3.5.4:
    formidable: private
  /forwarded/0.2.0:
    forwarded: private
  /fraction.js/4.3.7:
    fraction.js: private
  /fresh/0.5.2:
    fresh: private
  /fs-extra/10.1.0:
    fs-extra: private
  /fs-monkey/1.1.0:
    fs-monkey: private
  /fs.realpath/1.0.0:
    fs.realpath: private
  /fsevents/2.3.3:
    fsevents: private
  /function-bind/1.1.2:
    function-bind: private
  /function.prototype.name/1.1.8:
    function.prototype.name: private
  /functions-have-names/1.2.3:
    functions-have-names: private
  /generic-pool/3.9.0:
    generic-pool: private
  /gensync/1.0.0-beta.2:
    gensync: private
  /get-caller-file/2.0.5:
    get-caller-file: private
  /get-intrinsic/1.3.0:
    get-intrinsic: private
  /get-nonce/1.0.1:
    get-nonce: private
  /get-package-type/0.1.0:
    get-package-type: private
  /get-proto/1.0.1:
    get-proto: private
  /get-stream/6.0.1:
    get-stream: private
  /get-symbol-description/1.1.0:
    get-symbol-description: private
  /get-tsconfig/4.10.1:
    get-tsconfig: private
  /get-uri/6.0.5:
    get-uri: private
  /glob-parent/6.0.2:
    glob-parent: private
  /glob-to-regexp/0.4.1:
    glob-to-regexp: private
  /glob/10.4.5:
    glob: private
  /globals/13.24.0:
    globals: private
  /globalthis/1.0.4:
    globalthis: private
  /globby/11.1.0:
    globby: private
  /gopd/1.2.0:
    gopd: private
  /graceful-fs/4.2.11:
    graceful-fs: private
  /gradient-string/2.0.2:
    gradient-string: private
  /graphemer/1.4.0:
    graphemer: private
  /handlebars/4.7.8:
    handlebars: private
  /has-bigints/1.1.0:
    has-bigints: private
  /has-flag/4.0.0:
    has-flag: private
  /has-own-prop/2.0.0:
    has-own-prop: private
  /has-property-descriptors/1.0.2:
    has-property-descriptors: private
  /has-proto/1.2.0:
    has-proto: private
  /has-symbols/1.1.0:
    has-symbols: private
  /has-tostringtag/1.0.2:
    has-tostringtag: private
  /hasown/2.0.2:
    hasown: private
  /header-case/1.0.1:
    header-case: private
  /helmet/7.2.0:
    helmet: private
  /html-escaper/2.0.2:
    html-escaper: private
  /http-errors/2.0.0:
    http-errors: private
  /http-proxy-agent/7.0.2:
    http-proxy-agent: private
  /https-proxy-agent/7.0.6:
    https-proxy-agent: private
  /human-signals/2.1.0:
    human-signals: private
  /iconv-lite/0.4.24:
    iconv-lite: private
  /ieee754/1.2.1:
    ieee754: private
  /ignore/5.3.2:
    ignore: private
  /import-fresh/3.3.1:
    import-fresh: private
  /import-local/3.2.0:
    import-local: private
  /imurmurhash/0.1.4:
    imurmurhash: private
  /indent-string/4.0.0:
    indent-string: private
  /inflight/1.0.6:
    inflight: private
  /inherits/2.0.4:
    inherits: private
  /ini/1.3.8:
    ini: private
  /inquirer/8.2.6:
    inquirer: private
  /internal-slot/1.1.0:
    internal-slot: private
  /ip-address/10.0.1:
    ip-address: private
  /ipaddr.js/1.9.1:
    ipaddr.js: private
  /is-array-buffer/3.0.5:
    is-array-buffer: private
  /is-arrayish/0.2.1:
    is-arrayish: private
  /is-async-function/2.1.1:
    is-async-function: private
  /is-bigint/1.1.0:
    is-bigint: private
  /is-binary-path/2.1.0:
    is-binary-path: private
  /is-boolean-object/1.2.2:
    is-boolean-object: private
  /is-bun-module/2.0.0:
    is-bun-module: private
  /is-callable/1.2.7:
    is-callable: private
  /is-core-module/2.16.1:
    is-core-module: private
  /is-data-view/1.0.2:
    is-data-view: private
  /is-date-object/1.1.0:
    is-date-object: private
  /is-extglob/2.1.1:
    is-extglob: private
  /is-finalizationregistry/1.1.1:
    is-finalizationregistry: private
  /is-fullwidth-code-point/3.0.0:
    is-fullwidth-code-point: private
  /is-generator-fn/2.1.0:
    is-generator-fn: private
  /is-generator-function/1.1.0:
    is-generator-function: private
  /is-glob/4.0.3:
    is-glob: private
  /is-interactive/1.0.0:
    is-interactive: private
  /is-lower-case/1.1.3:
    is-lower-case: private
  /is-map/2.0.3:
    is-map: private
  /is-negative-zero/2.0.3:
    is-negative-zero: private
  /is-number-object/1.1.1:
    is-number-object: private
  /is-number/7.0.0:
    is-number: private
  /is-path-cwd/2.2.0:
    is-path-cwd: private
  /is-path-inside/3.0.3:
    is-path-inside: private
  /is-regex/1.2.1:
    is-regex: private
  /is-set/2.0.3:
    is-set: private
  /is-shared-array-buffer/1.0.4:
    is-shared-array-buffer: private
  /is-stream/2.0.1:
    is-stream: private
  /is-string/1.1.1:
    is-string: private
  /is-symbol/1.1.1:
    is-symbol: private
  /is-typed-array/1.1.15:
    is-typed-array: private
  /is-unicode-supported/0.1.0:
    is-unicode-supported: private
  /is-upper-case/1.1.2:
    is-upper-case: private
  /is-weakmap/2.0.2:
    is-weakmap: private
  /is-weakref/1.1.1:
    is-weakref: private
  /is-weakset/2.0.4:
    is-weakset: private
  /isarray/2.0.5:
    isarray: private
  /isbinaryfile/4.0.10:
    isbinaryfile: private
  /isexe/2.0.0:
    isexe: private
  /istanbul-lib-coverage/3.2.2:
    istanbul-lib-coverage: private
  /istanbul-lib-instrument/6.0.3:
    istanbul-lib-instrument: private
  /istanbul-lib-report/3.0.1:
    istanbul-lib-report: private
  /istanbul-lib-source-maps/4.0.1:
    istanbul-lib-source-maps: private
  /istanbul-reports/3.1.7:
    istanbul-reports: private
  /iterare/1.2.1:
    iterare: private
  /iterator.prototype/1.1.5:
    iterator.prototype: private
  /jackspeak/3.4.3:
    jackspeak: private
  /jest-changed-files/29.7.0:
    jest-changed-files: private
  /jest-circus/29.7.0:
    jest-circus: private
  /jest-cli/29.7.0(@types/node@20.19.10)(ts-node@10.9.2):
    jest-cli: private
  /jest-config/29.7.0(@types/node@20.19.10)(ts-node@10.9.2):
    jest-config: private
  /jest-diff/29.7.0:
    jest-diff: private
  /jest-docblock/29.7.0:
    jest-docblock: private
  /jest-each/29.7.0:
    jest-each: private
  /jest-environment-node/29.7.0:
    jest-environment-node: private
  /jest-get-type/29.6.3:
    jest-get-type: private
  /jest-haste-map/29.7.0:
    jest-haste-map: private
  /jest-leak-detector/29.7.0:
    jest-leak-detector: private
  /jest-matcher-utils/29.7.0:
    jest-matcher-utils: private
  /jest-message-util/29.7.0:
    jest-message-util: private
  /jest-mock/29.7.0:
    jest-mock: private
  /jest-pnp-resolver/1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  /jest-regex-util/29.6.3:
    jest-regex-util: private
  /jest-resolve-dependencies/29.7.0:
    jest-resolve-dependencies: private
  /jest-resolve/29.7.0:
    jest-resolve: private
  /jest-runner/29.7.0:
    jest-runner: private
  /jest-runtime/29.7.0:
    jest-runtime: private
  /jest-snapshot/29.7.0:
    jest-snapshot: private
  /jest-util/29.7.0:
    jest-util: private
  /jest-validate/29.7.0:
    jest-validate: private
  /jest-watcher/29.7.0:
    jest-watcher: private
  /jest-worker/29.7.0:
    jest-worker: private
  /jest/29.7.0(@types/node@20.19.10)(ts-node@10.9.2):
    jest: private
  /jiti/1.21.7:
    jiti: private
  /jose/4.15.9:
    jose: private
  /js-tokens/4.0.0:
    js-tokens: private
  /js-yaml/4.1.0:
    js-yaml: private
  /jsesc/3.1.0:
    jsesc: private
  /json-buffer/3.0.1:
    json-buffer: private
  /json-parse-even-better-errors/2.3.1:
    json-parse-even-better-errors: private
  /json-schema-traverse/0.4.1:
    json-schema-traverse: private
  /json-stable-stringify-without-jsonify/1.0.1:
    json-stable-stringify-without-jsonify: private
  /json5/2.2.3:
    json5: private
  /jsonc-parser/3.3.1:
    jsonc-parser: private
  /jsonfile/6.2.0:
    jsonfile: private
  /jsonwebtoken/9.0.2:
    jsonwebtoken: private
  /jsx-ast-utils/3.3.5:
    jsx-ast-utils: private
  /jwa/1.4.2:
    jwa: private
  /jws/3.2.2:
    jws: private
  /kafkajs/2.2.4:
    kafkajs: private
  /kareem/2.6.3:
    kareem: private
  /keyv/4.5.4:
    keyv: private
  /kleur/3.0.3:
    kleur: private
  /language-subtag-registry/0.3.23:
    language-subtag-registry: private
  /language-tags/1.0.9:
    language-tags: private
  /leven/3.1.0:
    leven: private
  /levn/0.4.1:
    levn: private
  /libphonenumber-js/1.12.11:
    libphonenumber-js: private
  /lilconfig/3.1.3:
    lilconfig: private
  /lines-and-columns/1.2.4:
    lines-and-columns: private
  /loader-runner/4.3.0:
    loader-runner: private
  /locate-path/6.0.0:
    locate-path: private
  /lodash.castarray/4.4.0:
    lodash.castarray: private
  /lodash.get/4.4.2:
    lodash.get: private
  /lodash.includes/4.3.0:
    lodash.includes: private
  /lodash.isboolean/3.0.3:
    lodash.isboolean: private
  /lodash.isinteger/4.0.4:
    lodash.isinteger: private
  /lodash.isnumber/3.0.3:
    lodash.isnumber: private
  /lodash.isplainobject/4.0.6:
    lodash.isplainobject: private
  /lodash.isstring/4.0.1:
    lodash.isstring: private
  /lodash.memoize/4.1.2:
    lodash.memoize: private
  /lodash.merge/4.6.2:
    lodash.merge: private
  /lodash.once/4.1.1:
    lodash.once: private
  /lodash/4.17.21:
    lodash: private
  /log-symbols/4.1.0:
    log-symbols: private
  /loose-envify/1.4.0:
    loose-envify: private
  /lower-case-first/1.0.2:
    lower-case-first: private
  /lower-case/1.1.4:
    lower-case: private
  /lru-cache/6.0.0:
    lru-cache: private
  /lucide-react/0.263.1(react@18.3.1):
    lucide-react: private
  /magic-string/0.30.8:
    magic-string: private
  /make-dir/4.0.0:
    make-dir: private
  /make-error/1.3.6:
    make-error: private
  /makeerror/1.0.12:
    makeerror: private
  /math-intrinsics/1.1.0:
    math-intrinsics: private
  /media-typer/0.3.0:
    media-typer: private
  /memfs/3.5.3:
    memfs: private
  /memory-pager/1.5.0:
    memory-pager: private
  /merge-descriptors/1.0.3:
    merge-descriptors: private
  /merge-stream/2.0.0:
    merge-stream: private
  /merge2/1.4.1:
    merge2: private
  /methods/1.1.2:
    methods: private
  /micromatch/4.0.8:
    micromatch: private
  /mime-db/1.52.0:
    mime-db: private
  /mime-types/2.1.35:
    mime-types: private
  /mime/2.6.0:
    mime: private
  /mimic-fn/2.1.0:
    mimic-fn: private
  /mini-svg-data-uri/1.4.4:
    mini-svg-data-uri: private
  /minimatch/9.0.5:
    minimatch: private
  /minimist/1.2.8:
    minimist: private
  /minipass/7.1.2:
    minipass: private
  /mkdirp/0.5.6:
    mkdirp: private
  /mongodb-connection-string-url/3.0.2:
    mongodb-connection-string-url: private
  /mongodb/6.18.0:
    mongodb: private
  /mongoose/8.17.1:
    mongoose: private
  /mpath/0.9.0:
    mpath: private
  /mquery/5.0.0:
    mquery: private
  /ms/2.1.3:
    ms: private
  /multer/2.0.2:
    multer: private
  /mute-stream/0.0.8:
    mute-stream: private
  /mz/2.7.0:
    mz: private
  /nanoid/3.3.11:
    nanoid: private
  /napi-postinstall/0.3.3:
    napi-postinstall: private
  /natural-compare/1.4.0:
    natural-compare: private
  /negotiator/0.6.3:
    negotiator: private
  /neo-async/2.6.2:
    neo-async: private
  /netmask/2.0.2:
    netmask: private
  /next-auth/4.24.11(next@15.4.6)(react-dom@18.3.1)(react@18.3.1):
    next-auth: private
  /next-themes/0.4.6(react-dom@18.3.1)(react@18.3.1):
    next-themes: private
  /next/15.4.6(react-dom@18.3.1)(react@18.3.1):
    next: private
  /no-case/2.3.2:
    no-case: private
  /node-abort-controller/3.1.1:
    node-abort-controller: private
  /node-emoji/1.11.0:
    node-emoji: private
  /node-fetch/2.7.0:
    node-fetch: private
  /node-int64/0.4.0:
    node-int64: private
  /node-plop/0.26.3:
    node-plop: private
  /node-releases/2.0.19:
    node-releases: private
  /normalize-path/3.0.0:
    normalize-path: private
  /normalize-range/0.1.2:
    normalize-range: private
  /npm-run-path/4.0.1:
    npm-run-path: private
  /oauth/0.9.15:
    oauth: private
  /object-assign/4.1.1:
    object-assign: private
  /object-hash/3.0.0:
    object-hash: private
  /object-inspect/1.13.4:
    object-inspect: private
  /object-keys/1.1.1:
    object-keys: private
  /object.assign/4.1.7:
    object.assign: private
  /object.entries/1.1.9:
    object.entries: private
  /object.fromentries/2.0.8:
    object.fromentries: private
  /object.groupby/1.0.3:
    object.groupby: private
  /object.values/1.2.1:
    object.values: private
  /oidc-token-hash/5.1.1:
    oidc-token-hash: private
  /on-finished/2.4.1:
    on-finished: private
  /once/1.4.0:
    once: private
  /onetime/5.1.2:
    onetime: private
  /openid-client/5.7.1:
    openid-client: private
  /optionator/0.9.4:
    optionator: private
  /ora/5.4.1:
    ora: private
  /os-tmpdir/1.0.2:
    os-tmpdir: private
  /own-keys/1.0.1:
    own-keys: private
  /p-limit/3.1.0:
    p-limit: private
  /p-locate/5.0.0:
    p-locate: private
  /p-map/3.0.0:
    p-map: private
  /p-try/2.2.0:
    p-try: private
  /pac-proxy-agent/7.2.0:
    pac-proxy-agent: private
  /pac-resolver/7.0.1:
    pac-resolver: private
  /package-json-from-dist/1.0.1:
    package-json-from-dist: private
  /param-case/2.1.1:
    param-case: private
  /parent-module/1.0.1:
    parent-module: private
  /parse-json/5.2.0:
    parse-json: private
  /parseurl/1.3.3:
    parseurl: private
  /pascal-case/2.0.1:
    pascal-case: private
  /passport-jwt/4.0.1:
    passport-jwt: private
  /passport-local/1.0.0:
    passport-local: private
  /passport-strategy/1.0.0:
    passport-strategy: private
  /passport/0.7.0:
    passport: private
  /path-case/2.1.1:
    path-case: private
  /path-exists/4.0.0:
    path-exists: private
  /path-is-absolute/1.0.1:
    path-is-absolute: private
  /path-key/3.1.1:
    path-key: private
  /path-parse/1.0.7:
    path-parse: private
  /path-scurry/1.11.1:
    path-scurry: private
  /path-to-regexp/3.3.0:
    path-to-regexp: private
  /path-type/4.0.0:
    path-type: private
  /pause/0.0.1:
    pause: private
  /picocolors/1.0.1:
    picocolors: private
  /picomatch/4.0.1:
    picomatch: private
  /pify/2.3.0:
    pify: private
  /pirates/4.0.7:
    pirates: private
  /pkg-dir/4.2.0:
    pkg-dir: private
  /pluralize/8.0.0:
    pluralize: private
  /possible-typed-array-names/1.1.0:
    possible-typed-array-names: private
  /postcss-import/15.1.0(postcss@8.5.6):
    postcss-import: private
  /postcss-js/4.0.1(postcss@8.5.6):
    postcss-js: private
  /postcss-load-config/4.0.2(postcss@8.5.6):
    postcss-load-config: private
  /postcss-nested/6.2.0(postcss@8.5.6):
    postcss-nested: private
  /postcss-selector-parser/6.0.10:
    postcss-selector-parser: private
  /postcss-value-parser/4.2.0:
    postcss-value-parser: private
  /postcss/8.5.6:
    postcss: private
  /preact-render-to-string/5.2.6(preact@10.27.0):
    preact-render-to-string: private
  /preact/10.27.0:
    preact: private
  /prelude-ls/1.2.1:
    prelude-ls: private
  /prettier-linter-helpers/1.0.0:
    prettier-linter-helpers: public
  /pretty-format/29.7.0:
    pretty-format: private
  /prisma/5.22.0:
    prisma: private
  /prompts/2.4.2:
    prompts: private
  /prop-types/15.8.1:
    prop-types: private
  /proxy-addr/2.0.7:
    proxy-addr: private
  /proxy-agent/6.5.0:
    proxy-agent: private
  /proxy-from-env/1.1.0:
    proxy-from-env: private
  /punycode/2.3.1:
    punycode: private
  /pure-rand/6.1.0:
    pure-rand: private
  /qs/6.13.0:
    qs: private
  /queue-microtask/1.2.3:
    queue-microtask: private
  /randombytes/2.1.0:
    randombytes: private
  /range-parser/1.2.1:
    range-parser: private
  /raw-body/2.5.2:
    raw-body: private
  /rc/1.2.8:
    rc: private
  /react-dom/18.3.1(react@18.3.1):
    react-dom: private
  /react-hook-form/7.62.0(react@18.3.1):
    react-hook-form: private
  /react-is/18.3.1:
    react-is: private
  /react-remove-scroll-bar/2.3.8(@types/react@18.3.23)(react@18.3.1):
    react-remove-scroll-bar: private
  /react-remove-scroll/2.7.1(@types/react@18.3.23)(react@18.3.1):
    react-remove-scroll: private
  /react-style-singleton/2.2.3(@types/react@18.3.23)(react@18.3.1):
    react-style-singleton: private
  /react/18.3.1:
    react: private
  /read-cache/1.0.0:
    read-cache: private
  /readable-stream/3.6.2:
    readable-stream: private
  /readdirp/3.6.0:
    readdirp: private
  /redis/4.7.1:
    redis: private
  /reflect-metadata/0.2.2:
    reflect-metadata: private
  /reflect.getprototypeof/1.0.10:
    reflect.getprototypeof: private
  /regexp.prototype.flags/1.5.4:
    regexp.prototype.flags: private
  /registry-auth-token/3.3.2:
    registry-auth-token: private
  /registry-url/3.1.0:
    registry-url: private
  /repeat-string/1.6.1:
    repeat-string: private
  /require-directory/2.1.1:
    require-directory: private
  /require-from-string/2.0.2:
    require-from-string: private
  /resolve-cwd/3.0.0:
    resolve-cwd: private
  /resolve-from/5.0.0:
    resolve-from: private
  /resolve-pkg-maps/1.0.0:
    resolve-pkg-maps: private
  /resolve.exports/2.0.3:
    resolve.exports: private
  /resolve/2.0.0-next.5:
    resolve: private
  /restore-cursor/3.1.0:
    restore-cursor: private
  /reusify/1.1.0:
    reusify: private
  /rimraf/3.0.2:
    rimraf: private
  /run-async/2.4.1:
    run-async: private
  /run-parallel/1.2.0:
    run-parallel: private
  /rxjs/7.8.2:
    rxjs: private
  /safe-array-concat/1.1.3:
    safe-array-concat: private
  /safe-buffer/5.2.1:
    safe-buffer: private
  /safe-push-apply/1.0.0:
    safe-push-apply: private
  /safe-regex-test/1.1.0:
    safe-regex-test: private
  /safer-buffer/2.1.2:
    safer-buffer: private
  /scheduler/0.23.2:
    scheduler: private
  /schema-utils/3.3.0:
    schema-utils: private
  /semver/7.7.2:
    semver: private
  /send/0.19.0:
    send: private
  /sentence-case/2.1.1:
    sentence-case: private
  /serialize-javascript/6.0.2:
    serialize-javascript: private
  /serve-static/1.16.2:
    serve-static: private
  /set-function-length/1.2.2:
    set-function-length: private
  /set-function-name/2.0.2:
    set-function-name: private
  /set-proto/1.0.0:
    set-proto: private
  /setprototypeof/1.2.0:
    setprototypeof: private
  /sharp/0.34.3:
    sharp: private
  /shebang-command/2.0.0:
    shebang-command: private
  /shebang-regex/3.0.0:
    shebang-regex: private
  /side-channel-list/1.0.0:
    side-channel-list: private
  /side-channel-map/1.0.1:
    side-channel-map: private
  /side-channel-weakmap/1.0.2:
    side-channel-weakmap: private
  /side-channel/1.1.0:
    side-channel: private
  /sift/17.1.3:
    sift: private
  /signal-exit/3.0.7:
    signal-exit: private
  /simple-swizzle/0.2.2:
    simple-swizzle: private
  /sisteransi/1.0.5:
    sisteransi: private
  /slash/3.0.0:
    slash: private
  /smart-buffer/4.2.0:
    smart-buffer: private
  /snake-case/2.1.0:
    snake-case: private
  /socks-proxy-agent/8.0.5:
    socks-proxy-agent: private
  /socks/2.8.7:
    socks: private
  /sonner/2.0.7(react-dom@18.3.1)(react@18.3.1):
    sonner: private
  /source-map-js/1.2.1:
    source-map-js: private
  /source-map-support/0.5.21:
    source-map-support: private
  /source-map/0.6.1:
    source-map: private
  /sparse-bitfield/3.0.3:
    sparse-bitfield: private
  /sprintf-js/1.0.3:
    sprintf-js: private
  /stable-hash/0.0.5:
    stable-hash: private
  /stack-utils/2.0.6:
    stack-utils: private
  /statuses/2.0.1:
    statuses: private
  /stop-iteration-iterator/1.1.0:
    stop-iteration-iterator: private
  /streamsearch/1.1.0:
    streamsearch: private
  /string-length/4.0.2:
    string-length: private
  /string-width/4.2.3:
    string-width: private
    string-width-cjs: private
  /string.prototype.includes/2.0.1:
    string.prototype.includes: private
  /string.prototype.matchall/4.0.12:
    string.prototype.matchall: private
  /string.prototype.repeat/1.0.0:
    string.prototype.repeat: private
  /string.prototype.trim/1.2.10:
    string.prototype.trim: private
  /string.prototype.trimend/1.0.9:
    string.prototype.trimend: private
  /string.prototype.trimstart/1.0.8:
    string.prototype.trimstart: private
  /string_decoder/1.3.0:
    string_decoder: private
  /strip-ansi/6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  /strip-bom/3.0.0:
    strip-bom: private
  /strip-final-newline/2.0.0:
    strip-final-newline: private
  /strip-json-comments/3.1.1:
    strip-json-comments: private
  /strtok3/10.3.4:
    strtok3: private
  /styled-jsx/5.1.6(react@18.3.1):
    styled-jsx: private
  /sucrase/3.35.0:
    sucrase: private
  /superagent/10.2.3:
    superagent: private
  /supertest/7.1.4:
    supertest: private
  /supports-color/7.2.0:
    supports-color: private
  /supports-preserve-symlinks-flag/1.0.0:
    supports-preserve-symlinks-flag: private
  /swagger-ui-dist/5.17.14:
    swagger-ui-dist: private
  /swap-case/1.1.2:
    swap-case: private
  /symbol-observable/4.0.0:
    symbol-observable: private
  /synckit/0.11.11:
    synckit: private
  /tailwind-merge/2.6.0:
    tailwind-merge: private
  /tailwindcss-animate/1.0.7(tailwindcss@3.4.17):
    tailwindcss-animate: private
  /tailwindcss/3.4.17:
    tailwindcss: private
  /tapable/2.2.2:
    tapable: private
  /terser-webpack-plugin/5.3.14(webpack@5.101.1):
    terser-webpack-plugin: private
  /terser/5.43.1:
    terser: private
  /test-exclude/6.0.0:
    test-exclude: private
  /text-table/0.2.0:
    text-table: private
  /thenify-all/1.6.0:
    thenify-all: private
  /thenify/3.3.1:
    thenify: private
  /through/2.3.8:
    through: private
  /tinycolor2/1.6.0:
    tinycolor2: private
  /tinyglobby/0.2.14:
    tinyglobby: private
  /tinygradient/1.1.5:
    tinygradient: private
  /title-case/2.1.1:
    title-case: private
  /tmp/0.0.33:
    tmp: private
  /tmpl/1.0.5:
    tmpl: private
  /to-regex-range/5.0.1:
    to-regex-range: private
  /toidentifier/1.0.1:
    toidentifier: private
  /token-types/6.1.1:
    token-types: private
  /tr46/5.1.1:
    tr46: private
  /tree-kill/1.2.2:
    tree-kill: private
  /ts-api-utils/1.4.3(typescript@5.9.2):
    ts-api-utils: private
  /ts-interface-checker/0.1.13:
    ts-interface-checker: private
  /ts-jest/29.4.1(@babel/core@7.28.0)(jest@29.7.0)(typescript@5.9.2):
    ts-jest: private
  /ts-loader/9.5.2(typescript@5.9.2)(webpack@5.101.1):
    ts-loader: private
  /ts-node/10.9.2(@types/node@20.19.10)(typescript@5.9.2):
    ts-node: private
  /tsconfig-paths-webpack-plugin/4.2.0:
    tsconfig-paths-webpack-plugin: private
  /tsconfig-paths/4.2.0:
    tsconfig-paths: private
  /tslib/2.8.1:
    tslib: private
  /turbo-darwin-64/2.5.5:
    turbo-darwin-64: private
  /turbo-darwin-arm64/2.5.5:
    turbo-darwin-arm64: private
  /turbo-linux-64/2.5.5:
    turbo-linux-64: private
  /turbo-linux-arm64/2.5.5:
    turbo-linux-arm64: private
  /turbo-windows-64/2.5.5:
    turbo-windows-64: private
  /turbo-windows-arm64/2.5.5:
    turbo-windows-arm64: private
  /type-check/0.4.0:
    type-check: private
  /type-detect/4.0.8:
    type-detect: private
  /type-fest/4.41.0:
    type-fest: private
  /type-is/1.6.18:
    type-is: private
  /typed-array-buffer/1.0.3:
    typed-array-buffer: private
  /typed-array-byte-length/1.0.3:
    typed-array-byte-length: private
  /typed-array-byte-offset/1.0.4:
    typed-array-byte-offset: private
  /typed-array-length/1.0.7:
    typed-array-length: private
  /typedarray/0.0.6:
    typedarray: private
  /uglify-js/3.19.3:
    uglify-js: private
  /uid/2.0.2:
    uid: private
  /uint8array-extras/1.4.1:
    uint8array-extras: private
  /unbox-primitive/1.1.0:
    unbox-primitive: private
  /undici-types/6.21.0:
    undici-types: private
  /universalify/2.0.1:
    universalify: private
  /unpipe/1.0.0:
    unpipe: private
  /unrs-resolver/1.11.1:
    unrs-resolver: private
  /update-browserslist-db/1.1.3(browserslist@4.25.2):
    update-browserslist-db: private
  /update-check/1.5.4:
    update-check: private
  /upper-case-first/1.1.2:
    upper-case-first: private
  /upper-case/1.1.3:
    upper-case: private
  /uri-js/4.4.1:
    uri-js: private
  /use-callback-ref/1.3.3(@types/react@18.3.23)(react@18.3.1):
    use-callback-ref: private
  /use-sidecar/1.1.3(@types/react@18.3.23)(react@18.3.1):
    use-sidecar: private
  /use-sync-external-store/1.5.0(react@18.3.1):
    use-sync-external-store: private
  /util-deprecate/1.0.2:
    util-deprecate: private
  /utils-merge/1.0.1:
    utils-merge: private
  /uuid/8.3.2:
    uuid: private
  /v8-compile-cache-lib/3.0.1:
    v8-compile-cache-lib: private
  /v8-to-istanbul/9.3.0:
    v8-to-istanbul: private
  /validate-npm-package-name/5.0.1:
    validate-npm-package-name: private
  /validator/13.15.15:
    validator: private
  /vary/1.1.2:
    vary: private
  /walker/1.0.8:
    walker: private
  /watchpack/2.4.4:
    watchpack: private
  /wcwidth/1.0.1:
    wcwidth: private
  /webidl-conversions/7.0.0:
    webidl-conversions: private
  /webpack-node-externals/3.0.0:
    webpack-node-externals: private
  /webpack-sources/3.3.3:
    webpack-sources: private
  /webpack/5.97.1:
    webpack: private
  /whatwg-url/14.2.0:
    whatwg-url: private
  /which-boxed-primitive/1.1.1:
    which-boxed-primitive: private
  /which-builtin-type/1.2.1:
    which-builtin-type: private
  /which-collection/1.0.2:
    which-collection: private
  /which-typed-array/1.1.19:
    which-typed-array: private
  /which/2.0.2:
    which: private
  /word-wrap/1.2.5:
    word-wrap: private
  /wordwrap/1.0.0:
    wordwrap: private
  /wrap-ansi/6.2.0:
    wrap-ansi: private
  /wrap-ansi/7.0.0:
    wrap-ansi-cjs: private
  /wrappy/1.0.2:
    wrappy: private
  /write-file-atomic/4.0.2:
    write-file-atomic: private
  /xtend/4.0.2:
    xtend: private
  /y18n/5.0.8:
    y18n: private
  /yallist/4.0.0:
    yallist: private
  /yaml/2.8.1:
    yaml: private
  /yargs-parser/21.1.1:
    yargs-parser: private
  /yargs/17.7.2:
    yargs: private
  /yn/3.1.1:
    yn: private
  /yocto-queue/0.1.0:
    yocto-queue: private
  /zod/3.25.76:
    zod: private
  /zustand/4.5.7(@types/react@18.3.23)(react@18.3.1):
    zustand: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.11.0
pendingBuilds: []
prunedAt: Thu, 14 Aug 2025 03:06:42 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - /@emnapi/core/1.4.5
  - /@emnapi/runtime/1.4.5
  - /@emnapi/wasi-threads/1.0.4
  - /@img/sharp-darwin-x64/0.34.3
  - /@img/sharp-libvips-darwin-x64/1.2.0
  - /@img/sharp-libvips-linux-arm/1.2.0
  - /@img/sharp-libvips-linux-arm64/1.2.0
  - /@img/sharp-libvips-linux-ppc64/1.2.0
  - /@img/sharp-libvips-linux-s390x/1.2.0
  - /@img/sharp-libvips-linux-x64/1.2.0
  - /@img/sharp-libvips-linuxmusl-arm64/1.2.0
  - /@img/sharp-libvips-linuxmusl-x64/1.2.0
  - /@img/sharp-linux-arm/0.34.3
  - /@img/sharp-linux-arm64/0.34.3
  - /@img/sharp-linux-ppc64/0.34.3
  - /@img/sharp-linux-s390x/0.34.3
  - /@img/sharp-linux-x64/0.34.3
  - /@img/sharp-linuxmusl-arm64/0.34.3
  - /@img/sharp-linuxmusl-x64/0.34.3
  - /@img/sharp-wasm32/0.34.3
  - /@img/sharp-win32-arm64/0.34.3
  - /@img/sharp-win32-ia32/0.34.3
  - /@img/sharp-win32-x64/0.34.3
  - /@napi-rs/wasm-runtime/0.2.12
  - /@next/swc-darwin-x64/15.4.6
  - /@next/swc-linux-arm64-gnu/15.4.6
  - /@next/swc-linux-arm64-musl/15.4.6
  - /@next/swc-linux-x64-gnu/15.4.6
  - /@next/swc-linux-x64-musl/15.4.6
  - /@next/swc-win32-arm64-msvc/15.4.6
  - /@next/swc-win32-x64-msvc/15.4.6
  - /@tailwindcss/oxide-android-arm64/4.1.11
  - /@tailwindcss/oxide-darwin-x64/4.1.11
  - /@tailwindcss/oxide-freebsd-x64/4.1.11
  - /@tailwindcss/oxide-linux-arm-gnueabihf/4.1.11
  - /@tailwindcss/oxide-linux-arm64-gnu/4.1.11
  - /@tailwindcss/oxide-linux-arm64-musl/4.1.11
  - /@tailwindcss/oxide-linux-x64-gnu/4.1.11
  - /@tailwindcss/oxide-linux-x64-musl/4.1.11
  - /@tailwindcss/oxide-wasm32-wasi/4.1.11
  - /@tailwindcss/oxide-win32-arm64-msvc/4.1.11
  - /@tailwindcss/oxide-win32-x64-msvc/4.1.11
  - /@tybys/wasm-util/0.10.0
  - /@unrs/resolver-binding-android-arm-eabi/1.11.1
  - /@unrs/resolver-binding-android-arm64/1.11.1
  - /@unrs/resolver-binding-darwin-x64/1.11.1
  - /@unrs/resolver-binding-freebsd-x64/1.11.1
  - /@unrs/resolver-binding-linux-arm-gnueabihf/1.11.1
  - /@unrs/resolver-binding-linux-arm-musleabihf/1.11.1
  - /@unrs/resolver-binding-linux-arm64-gnu/1.11.1
  - /@unrs/resolver-binding-linux-arm64-musl/1.11.1
  - /@unrs/resolver-binding-linux-ppc64-gnu/1.11.1
  - /@unrs/resolver-binding-linux-riscv64-gnu/1.11.1
  - /@unrs/resolver-binding-linux-riscv64-musl/1.11.1
  - /@unrs/resolver-binding-linux-s390x-gnu/1.11.1
  - /@unrs/resolver-binding-linux-x64-gnu/1.11.1
  - /@unrs/resolver-binding-linux-x64-musl/1.11.1
  - /@unrs/resolver-binding-wasm32-wasi/1.11.1
  - /@unrs/resolver-binding-win32-arm64-msvc/1.11.1
  - /@unrs/resolver-binding-win32-ia32-msvc/1.11.1
  - /@unrs/resolver-binding-win32-x64-msvc/1.11.1
  - /lightningcss-darwin-x64/1.30.1
  - /lightningcss-freebsd-x64/1.30.1
  - /lightningcss-linux-arm-gnueabihf/1.30.1
  - /lightningcss-linux-arm64-gnu/1.30.1
  - /lightningcss-linux-arm64-musl/1.30.1
  - /lightningcss-linux-x64-gnu/1.30.1
  - /lightningcss-linux-x64-musl/1.30.1
  - /lightningcss-win32-arm64-msvc/1.30.1
  - /lightningcss-win32-x64-msvc/1.30.1
  - /turbo-darwin-64/2.5.5
  - /turbo-linux-64/2.5.5
  - /turbo-linux-arm64/2.5.5
  - /turbo-windows-64/2.5.5
  - /turbo-windows-arm64/2.5.5
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
