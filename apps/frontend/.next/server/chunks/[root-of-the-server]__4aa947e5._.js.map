{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/app/api/auth/generate-verification-code/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\n\nconst BACKEND_URL =\n  process.env.NEXT_PUBLIC_BACKEND_URL || \"http://localhost:3002\";\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n\n    const response = await fetch(\n      `${BACKEND_URL}/api/v1/auth/generate-verification-code`,\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(body),\n      }\n    );\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      return NextResponse.json(\n        { message: data.message || \"Failed to generate verification code\" },\n        { status: response.status }\n      );\n    }\n\n    return NextResponse.json(data);\n  } catch (error) {\n    console.error(\"Error generating verification code:\", error);\n    return NextResponse.json(\n      { message: \"Internal server error\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cACJ,QAAQ,GAAG,CAAC,uBAAuB,IAAI;AAElC,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,MAAM,WAAW,MAAM,MACrB,GAAG,YAAY,uCAAuC,CAAC,EACvD;YACE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAGF,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO,qOAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS,KAAK,OAAO,IAAI;YAAuC,GAClE;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAE9B;QAEA,OAAO,qOAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO,qOAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}