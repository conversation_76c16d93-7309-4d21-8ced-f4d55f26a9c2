(()=>{var a={};a.id=983,a.ids=[983],a.modules={119:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(8172),e=c(2691),f=c(2701),g=c(9450),h=c(6084),i=c(8686),j=c(9717),k=c(1052),l=c(9006),m=c(1760),n=c(8231),o=c(9315),p=c(9056),q=c(261),r=c(7735),s=c(1468),t=c(6713),u=c(3934),v=c(7795),w=c(1847),x=c(624),y=c(6616),z=c(9603),A=c(6439),B=c(8336),C=c.n(B),D=c(4164),E=c(5149),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["auth",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,3717)),"/Users/<USER>/Data/new era/CanSell-Ultimate/apps/frontend/src/app/auth/register/page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,6745))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,5723)),"/Users/<USER>/Data/new era/CanSell-Ultimate/apps/frontend/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,8336,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,6950,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,1167,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,4002,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,6745))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Data/new era/CanSell-Ultimate/apps/frontend/src/app/auth/register/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/auth/register/page",pathname:"/auth/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/auth/register/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},172:(a,b,c)=>{Promise.resolve().then(c.bind(c,7415))},207:(a,b,c)=>{Promise.resolve().then(c.bind(c,9296))},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},444:(a,b,c)=>{Promise.resolve().then(c.bind(c,3717))},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},2675:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,8336,23)),Promise.resolve().then(c.t.bind(c,2027,23)),Promise.resolve().then(c.t.bind(c,9271,23)),Promise.resolve().then(c.t.bind(c,3578,23)),Promise.resolve().then(c.t.bind(c,3230,23)),Promise.resolve().then(c.t.bind(c,8162,23)),Promise.resolve().then(c.t.bind(c,2152,23)),Promise.resolve().then(c.t.bind(c,5594,23)),Promise.resolve().then(c.t.bind(c,186,23))},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3717:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(7184).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/CanSell-Ultimate/apps/frontend/src/app/auth/register/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/CanSell-Ultimate/apps/frontend/src/app/auth/register/page.tsx","default")},3873:a=>{"use strict";a.exports=require("path")},4642:()=>{},5723:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i,metadata:()=>h});var d=c(2876),e=c(5377),f=c.n(e);c(4642),function(){var a=Error("Cannot find module '@/components/navigation'");throw a.code="MODULE_NOT_FOUND",a}(),function(){var a=Error("Cannot find module '@/components/theme-provider'");throw a.code="MODULE_NOT_FOUND",a}();var g=c(8577);let h={title:"CanSell Ultimate",description:"AI-powered marketplace for buying and selling items"};function i({children:a}){return(0,d.jsx)("html",{lang:"en",suppressHydrationWarnings:!0,children:(0,d.jsx)("body",{className:`${f().variable} font-sans antialiased`,children:(0,d.jsxs)(Object(function(){var a=Error("Cannot find module '@/components/theme-provider'");throw a.code="MODULE_NOT_FOUND",a}()),{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:[(0,d.jsxs)("div",{className:"flex min-h-screen flex-col",children:[(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/navigation'");throw a.code="MODULE_NOT_FOUND",a}()),{}),(0,d.jsx)("main",{className:"flex-1",children:a}),(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/navigation'");throw a.code="MODULE_NOT_FOUND",a}()),{})]}),(0,d.jsx)(g.Toaster,{})]})})})}},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},6745:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(5477);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},7099:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,8782,23)),Promise.resolve().then(c.t.bind(c,3217,23)),Promise.resolve().then(c.t.bind(c,3577,23)),Promise.resolve().then(c.t.bind(c,4576,23)),Promise.resolve().then(c.t.bind(c,2336,23)),Promise.resolve().then(c.t.bind(c,4084,23)),Promise.resolve().then(c.t.bind(c,7818,23)),Promise.resolve().then(c.t.bind(c,2228,23)),Promise.resolve().then(c.bind(c,6660))},7415:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>al});var d=c(3246),e=c(1103),f={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},g=(a,b)=>{let c=(0,e.forwardRef)(({color:c="currentColor",size:d=24,strokeWidth:g=2,absoluteStrokeWidth:h,children:i,...j},k)=>(0,e.createElement)("svg",{ref:k,...f,width:d,height:d,stroke:c,strokeWidth:h?24*Number(g)/Number(d):g,className:`lucide lucide-${a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,...j},[...b.map(([a,b])=>(0,e.createElement)(a,b)),...(Array.isArray(i)?i:[i])||[]]));return c.displayName=`${a}`,c};let h=g("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]),i=g("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),j=g("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),k=g("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),l=g("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]),m=g("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),n=g("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z",key:"3xmgem"}]]);var o=c(53),p=c.n(o),q=a=>a instanceof Date,r=a=>null==a,s=a=>!r(a)&&!Array.isArray(a)&&"object"==typeof a&&!q(a),t="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function u(a){let b,c=Array.isArray(a),d="undefined"!=typeof FileList&&a instanceof FileList;if(a instanceof Date)b=new Date(a);else if(!(!(t&&(a instanceof Blob||d))&&(c||s(a))))return a;else if(b=c?[]:Object.create(Object.getPrototypeOf(a)),c||(a=>{let b=a.constructor&&a.constructor.prototype;return s(b)&&b.hasOwnProperty("isPrototypeOf")})(a))for(let c in a)a.hasOwnProperty(c)&&(b[c]=u(a[c]));else b=a;return b}var v=a=>/^\w*$/.test(a),w=a=>void 0===a,x=a=>Array.isArray(a)?a.filter(Boolean):[],y=a=>x(a.replace(/["|']|\]/g,"").split(/\.|\[/)),z=(a,b,c)=>{if(!b||!s(a))return c;let d=(v(b)?[b]:y(b)).reduce((a,b)=>r(a)?a:a[b],a);return w(d)||d===a?w(a[b])?c:a[b]:d},A=(a,b,c)=>{let d=-1,e=v(b)?[b]:y(b),f=e.length,g=f-1;for(;++d<f;){let b=e[d],f=c;if(d!==g){let c=a[b];f=s(c)||Array.isArray(c)?c:isNaN(+e[d+1])?{}:[]}if("__proto__"===b||"constructor"===b||"prototype"===b)return;a[b]=f,a=a[b]}};let B={BLUR:"blur",FOCUS_OUT:"focusout"},C={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},D={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"};e.createContext(null).displayName="HookFormContext";let E="undefined"!=typeof window?e.useLayoutEffect:e.useEffect;var F=a=>r(a)||"object"!=typeof a;function G(a,b,c=new WeakSet){if(F(a)||F(b))return a===b;if(q(a)&&q(b))return a.getTime()===b.getTime();let d=Object.keys(a),e=Object.keys(b);if(d.length!==e.length)return!1;if(c.has(a)||c.has(b))return!0;for(let f of(c.add(a),c.add(b),d)){let d=a[f];if(!e.includes(f))return!1;if("ref"!==f){let a=b[f];if(q(d)&&q(a)||s(d)&&s(a)||Array.isArray(d)&&Array.isArray(a)?!G(d,a,c):d!==a)return!1}}return!0}var H=(a,b,c,d,e)=>b?{...c[a],types:{...c[a]&&c[a].types?c[a].types:{},[d]:e||!0}}:{},I=a=>Array.isArray(a)?a:[a],J=()=>{let a=[];return{get observers(){return a},next:b=>{for(let c of a)c.next&&c.next(b)},subscribe:b=>(a.push(b),{unsubscribe:()=>{a=a.filter(a=>a!==b)}}),unsubscribe:()=>{a=[]}}},K=a=>s(a)&&!Object.keys(a).length,L=a=>"function"==typeof a,M=a=>{if(!t)return!1;let b=a?a.ownerDocument:0;return a instanceof(b&&b.defaultView?b.defaultView.HTMLElement:HTMLElement)},N=a=>M(a)&&a.isConnected;function O(a,b){let c=Array.isArray(b)?b:v(b)?[b]:y(b),d=1===c.length?a:function(a,b){let c=b.slice(0,-1).length,d=0;for(;d<c;)a=w(a)?d++:a[b[d++]];return a}(a,c),e=c.length-1,f=c[e];return d&&delete d[f],0!==e&&(s(d)&&K(d)||Array.isArray(d)&&function(a){for(let b in a)if(a.hasOwnProperty(b)&&!w(a[b]))return!1;return!0}(d))&&O(a,c.slice(0,-1)),a}var P=a=>{for(let b in a)if(L(a[b]))return!0;return!1};function Q(a,b={}){let c=Array.isArray(a);if(s(a)||c)for(let c in a)Array.isArray(a[c])||s(a[c])&&!P(a[c])?(b[c]=Array.isArray(a[c])?[]:{},Q(a[c],b[c])):r(a[c])||(b[c]=!0);return b}var R=(a,b)=>(function a(b,c,d){let e=Array.isArray(b);if(s(b)||e)for(let e in b)Array.isArray(b[e])||s(b[e])&&!P(b[e])?w(c)||F(d[e])?d[e]=Array.isArray(b[e])?Q(b[e],[]):{...Q(b[e])}:a(b[e],r(c)?{}:c[e],d[e]):d[e]=!G(b[e],c[e]);return d})(a,b,Q(b));let S={value:!1,isValid:!1},T={value:!0,isValid:!0};var U=a=>{if(Array.isArray(a)){if(a.length>1){let b=a.filter(a=>a&&a.checked&&!a.disabled).map(a=>a.value);return{value:b,isValid:!!b.length}}return a[0].checked&&!a[0].disabled?a[0].attributes&&!w(a[0].attributes.value)?w(a[0].value)||""===a[0].value?T:{value:a[0].value,isValid:!0}:T:S}return S},V=(a,{valueAsNumber:b,valueAsDate:c,setValueAs:d})=>w(a)?a:b?""===a?NaN:a?+a:a:c&&"string"==typeof a?new Date(a):d?d(a):a;let W={isValid:!1,value:null};var X=a=>Array.isArray(a)?a.reduce((a,b)=>b&&b.checked&&!b.disabled?{isValid:!0,value:b.value}:a,W):W;function Y(a){let b=a.ref;return"file"===b.type?b.files:"radio"===b.type?X(a.refs).value:"select-multiple"===b.type?[...b.selectedOptions].map(({value:a})=>a):"checkbox"===b.type?U(a.refs).value:V(w(b.value)?a.ref.value:b.value,a)}var Z=a=>w(a)?a:a instanceof RegExp?a.source:s(a)?a.value instanceof RegExp?a.value.source:a.value:a,$=a=>({isOnSubmit:!a||a===C.onSubmit,isOnBlur:a===C.onBlur,isOnChange:a===C.onChange,isOnAll:a===C.all,isOnTouch:a===C.onTouched});let _="AsyncFunction";var aa=a=>!!a&&!!a.validate&&!!(L(a.validate)&&a.validate.constructor.name===_||s(a.validate)&&Object.values(a.validate).find(a=>a.constructor.name===_)),ab=(a,b,c)=>!c&&(b.watchAll||b.watch.has(a)||[...b.watch].some(b=>a.startsWith(b)&&/^\.\w+/.test(a.slice(b.length))));let ac=(a,b,c,d)=>{for(let e of c||Object.keys(a)){let c=z(a,e);if(c){let{_f:a,...f}=c;if(a){if(a.refs&&a.refs[0]&&b(a.refs[0],e)&&!d)return!0;else if(a.ref&&b(a.ref,a.name)&&!d)return!0;else if(ac(f,b))break}else if(s(f)&&ac(f,b))break}}};function ad(a,b,c){let d=z(a,c);if(d||v(c))return{error:d,name:c};let e=c.split(".");for(;e.length;){let d=e.join("."),f=z(b,d),g=z(a,d);if(f&&!Array.isArray(f)&&c!==d)break;if(g&&g.type)return{name:d,error:g};if(g&&g.root&&g.root.type)return{name:`${d}.root`,error:g.root};e.pop()}return{name:c}}var ae=(a,b,c)=>{let d=I(z(a,c));return A(d,"root",b[c]),A(a,c,d),a},af=a=>"string"==typeof a;function ag(a,b,c="validate"){if(af(a)||Array.isArray(a)&&a.every(af)||"boolean"==typeof a&&!a)return{type:c,message:af(a)?a:"",ref:b}}var ah=a=>!s(a)||a instanceof RegExp?{value:a,message:""}:a,ai=async(a,b,c,d,e,f)=>{let{ref:g,refs:h,required:i,maxLength:j,minLength:k,min:l,max:m,pattern:n,validate:o,name:p,valueAsNumber:q,mount:t}=a._f,u=z(c,p);if(!t||b.has(p))return{};let v=h?h[0]:g,x=a=>{e&&v.reportValidity&&(v.setCustomValidity("boolean"==typeof a?"":a||""),v.reportValidity())},y={},A="radio"===g.type,B="checkbox"===g.type,C=(q||"file"===g.type)&&w(g.value)&&w(u)||M(g)&&""===g.value||""===u||Array.isArray(u)&&!u.length,E=H.bind(null,p,d,y),F=(a,b,c,d=D.maxLength,e=D.minLength)=>{let f=a?b:c;y[p]={type:a?d:e,message:f,ref:g,...E(a?d:e,f)}};if(f?!Array.isArray(u)||!u.length:i&&(!(A||B)&&(C||r(u))||"boolean"==typeof u&&!u||B&&!U(h).isValid||A&&!X(h).isValid)){let{value:a,message:b}=af(i)?{value:!!i,message:i}:ah(i);if(a&&(y[p]={type:D.required,message:b,ref:v,...E(D.required,b)},!d))return x(b),y}if(!C&&(!r(l)||!r(m))){let a,b,c=ah(m),e=ah(l);if(r(u)||isNaN(u)){let d=g.valueAsDate||new Date(u),f=a=>new Date(new Date().toDateString()+" "+a),h="time"==g.type,i="week"==g.type;"string"==typeof c.value&&u&&(a=h?f(u)>f(c.value):i?u>c.value:d>new Date(c.value)),"string"==typeof e.value&&u&&(b=h?f(u)<f(e.value):i?u<e.value:d<new Date(e.value))}else{let d=g.valueAsNumber||(u?+u:u);r(c.value)||(a=d>c.value),r(e.value)||(b=d<e.value)}if((a||b)&&(F(!!a,c.message,e.message,D.max,D.min),!d))return x(y[p].message),y}if((j||k)&&!C&&("string"==typeof u||f&&Array.isArray(u))){let a=ah(j),b=ah(k),c=!r(a.value)&&u.length>+a.value,e=!r(b.value)&&u.length<+b.value;if((c||e)&&(F(c,a.message,b.message),!d))return x(y[p].message),y}if(n&&!C&&"string"==typeof u){let{value:a,message:b}=ah(n);if(a instanceof RegExp&&!u.match(a)&&(y[p]={type:D.pattern,message:b,ref:g,...E(D.pattern,b)},!d))return x(b),y}if(o){if(L(o)){let a=ag(await o(u,c),v);if(a&&(y[p]={...a,...E(D.validate,a.message)},!d))return x(a.message),y}else if(s(o)){let a={};for(let b in o){if(!K(a)&&!d)break;let e=ag(await o[b](u,c),v,b);e&&(a={...e,...E(b,e.message)},x(e.message),d&&(y[p]=a))}if(!K(a)&&(y[p]={ref:v,...a},!d))return y}}return x(!0),y};let aj={mode:C.onSubmit,reValidateMode:C.onChange,shouldFocusError:!0};var ak=c(9296);function al(){let[a,b]=(0,e.useState)(!1),[c,f]=(0,e.useState)(!1),[g,o]=(0,e.useState)(!1),[v,y]=(0,e.useState)(!1),[D,F]=(0,e.useState)(""),[H,P]=(0,e.useState)(!1),{register:Q,handleSubmit:S,watch:T,formState:{errors:U},setValue:W}=function(a={}){let b=e.useRef(void 0),c=e.useRef(void 0),[d,f]=e.useState({isDirty:!1,isValidating:!1,isLoading:L(a.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:a.errors||{},disabled:a.disabled||!1,isReady:!1,defaultValues:L(a.defaultValues)?void 0:a.defaultValues});if(!b.current)if(a.formControl)b.current={...a.formControl,formState:d},a.defaultValues&&!L(a.defaultValues)&&a.formControl.reset(a.defaultValues,a.resetOptions);else{let{formControl:c,...e}=function(a={}){let b,c={...aj,...a},d={submitCount:0,isDirty:!1,isReady:!1,isLoading:L(c.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:c.errors||{},disabled:c.disabled||!1},e={},f=(s(c.defaultValues)||s(c.values))&&u(c.defaultValues||c.values)||{},g=c.shouldUnregister?{}:u(f),h={action:!1,mount:!1,watch:!1},i={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},j=0,k={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},l={...k},m={array:J(),state:J()},n=c.criteriaMode===C.all,o=async a=>{if(!c.disabled&&(k.isValid||l.isValid||a)){let a=c.resolver?K((await D()).errors):await F(e,!0);a!==d.isValid&&m.state.next({isValid:a})}},p=(a,b)=>{!c.disabled&&(k.isValidating||k.validatingFields||l.isValidating||l.validatingFields)&&((a||Array.from(i.mount)).forEach(a=>{a&&(b?A(d.validatingFields,a,b):O(d.validatingFields,a))}),m.state.next({validatingFields:d.validatingFields,isValidating:!K(d.validatingFields)}))},v=(a,b,c,d)=>{let i=z(e,a);if(i){let e=z(g,a,w(c)?z(f,a):c);w(e)||d&&d.defaultChecked||b?A(g,a,b?e:Y(i._f)):Q(a,e),h.mount&&o()}},y=(a,b,e,g,h)=>{let i=!1,j=!1,n={name:a};if(!c.disabled){if(!e||g){(k.isDirty||l.isDirty)&&(j=d.isDirty,d.isDirty=n.isDirty=H(),i=j!==n.isDirty);let c=G(z(f,a),b);j=!!z(d.dirtyFields,a),c?O(d.dirtyFields,a):A(d.dirtyFields,a,!0),n.dirtyFields=d.dirtyFields,i=i||(k.dirtyFields||l.dirtyFields)&&!c!==j}if(e){let b=z(d.touchedFields,a);b||(A(d.touchedFields,a,e),n.touchedFields=d.touchedFields,i=i||(k.touchedFields||l.touchedFields)&&b!==e)}i&&h&&m.state.next(n)}return i?n:{}},D=async a=>{p(a,!0);let b=await c.resolver(g,c.context,((a,b,c,d)=>{let e={};for(let c of a){let a=z(b,c);a&&A(e,c,a._f)}return{criteriaMode:c,names:[...a],fields:e,shouldUseNativeValidation:d}})(a||i.mount,e,c.criteriaMode,c.shouldUseNativeValidation));return p(a),b},E=async a=>{let{errors:b}=await D(a);if(a)for(let c of a){let a=z(b,c);a?A(d.errors,c,a):O(d.errors,c)}else d.errors=b;return b},F=async(a,b,e={valid:!0})=>{for(let f in a){let h=a[f];if(h){let{_f:a,...j}=h;if(a){let j=i.array.has(a.name),l=h._f&&aa(h._f);l&&k.validatingFields&&p([f],!0);let m=await ai(h,i.disabled,g,n,c.shouldUseNativeValidation&&!b,j);if(l&&k.validatingFields&&p([f]),m[a.name]&&(e.valid=!1,b))break;b||(z(m,a.name)?j?ae(d.errors,m,a.name):A(d.errors,a.name,m[a.name]):O(d.errors,a.name))}K(j)||await F(j,b,e)}}return e.valid},H=(a,b)=>!c.disabled&&(a&&b&&A(g,a,b),!G(_(),f)),P=(a,b,c)=>{let d,e,j,k,l;return d=a,e=i,j={...h.mount?g:w(b)?f:"string"==typeof a?{[a]:b}:b},k=c,l=b,"string"==typeof d?(k&&e.watch.add(d),z(j,d,l)):Array.isArray(d)?d.map(a=>(k&&e.watch.add(a),z(j,a))):(k&&(e.watchAll=!0),j)},Q=(a,b,c={})=>{let d=z(e,a),f=b;if(d){let c=d._f;c&&(c.disabled||A(g,a,V(b,c)),f=M(c.ref)&&r(b)?"":b,"select-multiple"===c.ref.type?[...c.ref.options].forEach(a=>a.selected=f.includes(a.value)):c.refs?"checkbox"===c.ref.type?c.refs.forEach(a=>{a.defaultChecked&&a.disabled||(Array.isArray(f)?a.checked=!!f.find(b=>b===a.value):a.checked=f===a.value||!!f)}):c.refs.forEach(a=>a.checked=a.value===f):"file"===c.ref.type?c.ref.value="":(c.ref.value=f,c.ref.type||m.state.next({name:a,values:u(g)})))}(c.shouldDirty||c.shouldTouch)&&y(a,f,c.shouldTouch,c.shouldDirty,!0),c.shouldValidate&&X(a)},S=(a,b,c)=>{for(let d in b){if(!b.hasOwnProperty(d))return;let f=b[d],g=a+"."+d,h=z(e,g);(i.array.has(a)||s(f)||h&&!h._f)&&!q(f)?S(g,f,c):Q(g,f,c)}},T=(a,b,c={})=>{let j=z(e,a),n=i.array.has(a),o=u(b);A(g,a,o),n?(m.array.next({name:a,values:u(g)}),(k.isDirty||k.dirtyFields||l.isDirty||l.dirtyFields)&&c.shouldDirty&&m.state.next({name:a,dirtyFields:R(f,g),isDirty:H(a,o)})):!j||j._f||r(o)?Q(a,o,c):S(a,o,c),ab(a,i)&&m.state.next({...d,name:a}),m.state.next({name:h.mount?a:void 0,values:u(g)})},U=async a=>{h.mount=!0;let f=a.target,r=f.name,t=!0,v=z(e,r),w=a=>{t=Number.isNaN(a)||q(a)&&isNaN(a.getTime())||G(a,z(g,r,a))},x=$(c.mode),C=$(c.reValidateMode);if(v){let h,q,Q,R,S=f.type?Y(v._f):s(R=a)&&R.target?"checkbox"===R.target.type?R.target.checked:R.target.value:R,T=a.type===B.BLUR||a.type===B.FOCUS_OUT,U=!((Q=v._f).mount&&(Q.required||Q.min||Q.max||Q.maxLength||Q.minLength||Q.pattern||Q.validate))&&!c.resolver&&!z(d.errors,r)&&!v._f.deps||(E=T,H=z(d.touchedFields,r),I=d.isSubmitted,J=C,!(L=x).isOnAll&&(!I&&L.isOnTouch?!(H||E):(I?J.isOnBlur:L.isOnBlur)?!E:(I?!J.isOnChange:!L.isOnChange)||E)),V=ab(r,i,T);A(g,r,S),T?f&&f.readOnly||(v._f.onBlur&&v._f.onBlur(a),b&&b(0)):v._f.onChange&&v._f.onChange(a);let W=y(r,S,T),Z=!K(W)||V;if(T||m.state.next({name:r,type:a.type,values:u(g)}),U)return(k.isValid||l.isValid)&&("onBlur"===c.mode?T&&o():T||o()),Z&&m.state.next({name:r,...V?{}:W});if(!T&&V&&m.state.next({...d}),c.resolver){let{errors:a}=await D([r]);if(w(S),t){let b=ad(d.errors,e,r),c=ad(a,e,b.name||r);h=c.error,r=c.name,q=K(a)}}else p([r],!0),h=(await ai(v,i.disabled,g,n,c.shouldUseNativeValidation))[r],p([r]),w(S),t&&(h?q=!1:(k.isValid||l.isValid)&&(q=await F(e,!0)));if(t){v._f.deps&&X(v._f.deps);var E,H,I,J,L,M=r,N=q,P=h;let a=z(d.errors,M),e=(k.isValid||l.isValid)&&"boolean"==typeof N&&d.isValid!==N;if(c.delayError&&P){let a;a=()=>{A(d.errors,M,P),m.state.next({errors:d.errors})},(b=b=>{clearTimeout(j),j=setTimeout(a,b)})(c.delayError)}else clearTimeout(j),b=null,P?A(d.errors,M,P):O(d.errors,M);if((P?!G(a,P):a)||!K(W)||e){let a={...W,...e&&"boolean"==typeof N?{isValid:N}:{},errors:d.errors,name:M};d={...d,...a},m.state.next(a)}}}},W=(a,b)=>{if(z(d.errors,b)&&a.focus)return a.focus(),1},X=async(a,b={})=>{let f,g,h=I(a);if(c.resolver){let b=await E(w(a)?a:h);f=K(b),g=a?!h.some(a=>z(b,a)):f}else a?((g=(await Promise.all(h.map(async a=>{let b=z(e,a);return await F(b&&b._f?{[a]:b}:b)}))).every(Boolean))||d.isValid)&&o():g=f=await F(e);return m.state.next({..."string"!=typeof a||(k.isValid||l.isValid)&&f!==d.isValid?{}:{name:a},...c.resolver||!a?{isValid:f}:{},errors:d.errors}),b.shouldFocus&&!g&&ac(e,W,a?h:i.mount),g},_=a=>{let b={...h.mount?g:f};return w(a)?b:"string"==typeof a?z(b,a):a.map(a=>z(b,a))},af=(a,b)=>({invalid:!!z((b||d).errors,a),isDirty:!!z((b||d).dirtyFields,a),error:z((b||d).errors,a),isValidating:!!z(d.validatingFields,a),isTouched:!!z((b||d).touchedFields,a)}),ag=(a,b,c)=>{let f=(z(e,a,{_f:{}})._f||{}).ref,{ref:g,message:h,type:i,...j}=z(d.errors,a)||{};A(d.errors,a,{...j,...b,ref:f}),m.state.next({name:a,errors:d.errors,isValid:!1}),c&&c.shouldFocus&&f&&f.focus&&f.focus()},ah=a=>m.state.subscribe({next:b=>{let c,e,h;c=a.name,e=b.name,h=a.exact,(!c||!e||c===e||I(c).some(a=>a&&(h?a===e:a.startsWith(e)||e.startsWith(a))))&&((a,b,c,d)=>{c(a);let{name:e,...f}=a;return K(f)||Object.keys(f).length>=Object.keys(b).length||Object.keys(f).find(a=>b[a]===(!d||C.all))})(b,a.formState||k,ar,a.reRenderRoot)&&a.callback({values:{...g},...d,...b,defaultValues:f})}}).unsubscribe,ak=(a,b={})=>{for(let h of a?I(a):i.mount)i.mount.delete(h),i.array.delete(h),b.keepValue||(O(e,h),O(g,h)),b.keepError||O(d.errors,h),b.keepDirty||O(d.dirtyFields,h),b.keepTouched||O(d.touchedFields,h),b.keepIsValidating||O(d.validatingFields,h),c.shouldUnregister||b.keepDefaultValue||O(f,h);m.state.next({values:u(g)}),m.state.next({...d,...!b.keepDirty?{}:{isDirty:H()}}),b.keepIsValid||o()},al=({disabled:a,name:b})=>{("boolean"==typeof a&&h.mount||a||i.disabled.has(b))&&(a?i.disabled.add(b):i.disabled.delete(b))},am=(a,b={})=>{let d=z(e,a),g="boolean"==typeof b.disabled||"boolean"==typeof c.disabled;return(A(e,a,{...d||{},_f:{...d&&d._f?d._f:{ref:{name:a}},name:a,mount:!0,...b}}),i.mount.add(a),d)?al({disabled:"boolean"==typeof b.disabled?b.disabled:c.disabled,name:a}):v(a,!0,b.value),{...g?{disabled:b.disabled||c.disabled}:{},...c.progressive?{required:!!b.required,min:Z(b.min),max:Z(b.max),minLength:Z(b.minLength),maxLength:Z(b.maxLength),pattern:Z(b.pattern)}:{},name:a,onChange:U,onBlur:U,ref:g=>{if(g){let c;am(a,b),d=z(e,a);let h=w(g.value)&&g.querySelectorAll&&g.querySelectorAll("input,select,textarea")[0]||g,i="radio"===(c=h).type||"checkbox"===c.type,j=d._f.refs||[];(i?j.find(a=>a===h):h===d._f.ref)||(A(e,a,{_f:{...d._f,...i?{refs:[...j.filter(N),h,...Array.isArray(z(f,a))?[{}]:[]],ref:{type:h.type,name:a}}:{ref:h}}}),v(a,!1,void 0,h))}else{let f;(d=z(e,a,{}))._f&&(d._f.mount=!1),(c.shouldUnregister||b.shouldUnregister)&&(f=i.array,!f.has(a.substring(0,a.search(/\.\d+(\.|$)/))||a)||!h.action)&&i.unMount.add(a)}}}},an=()=>c.shouldFocusError&&ac(e,W,i.mount),ao=(a,b)=>async f=>{let h;f&&(f.preventDefault&&f.preventDefault(),f.persist&&f.persist());let j=u(g);if(m.state.next({isSubmitting:!0}),c.resolver){let{errors:a,values:b}=await D();d.errors=a,j=u(b)}else await F(e);if(i.disabled.size)for(let a of i.disabled)O(j,a);if(O(d.errors,"root"),K(d.errors)){m.state.next({errors:{}});try{await a(j,f)}catch(a){h=a}}else b&&await b({...d.errors},f),an(),setTimeout(an);if(m.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:K(d.errors)&&!h,submitCount:d.submitCount+1,errors:d.errors}),h)throw h},ap=(a,b={})=>{let j=a?u(a):f,l=u(j),n=K(a),o=n?f:l;if(b.keepDefaultValues||(f=j),!b.keepValues){if(b.keepDirtyValues)for(let a of Array.from(new Set([...i.mount,...Object.keys(R(f,g))])))z(d.dirtyFields,a)?A(o,a,z(g,a)):T(a,z(o,a));else{if(t&&w(a))for(let a of i.mount){let b=z(e,a);if(b&&b._f){let a=Array.isArray(b._f.refs)?b._f.refs[0]:b._f.ref;if(M(a)){let b=a.closest("form");if(b){b.reset();break}}}}if(b.keepFieldsRef)for(let a of i.mount)T(a,z(o,a));else e={}}g=c.shouldUnregister?b.keepDefaultValues?u(f):{}:u(o),m.array.next({values:{...o}}),m.state.next({values:{...o}})}i={mount:b.keepDirtyValues?i.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},h.mount=!k.isValid||!!b.keepIsValid||!!b.keepDirtyValues,h.watch=!!c.shouldUnregister,m.state.next({submitCount:b.keepSubmitCount?d.submitCount:0,isDirty:!n&&(b.keepDirty?d.isDirty:!!(b.keepDefaultValues&&!G(a,f))),isSubmitted:!!b.keepIsSubmitted&&d.isSubmitted,dirtyFields:n?{}:b.keepDirtyValues?b.keepDefaultValues&&g?R(f,g):d.dirtyFields:b.keepDefaultValues&&a?R(f,a):b.keepDirty?d.dirtyFields:{},touchedFields:b.keepTouched?d.touchedFields:{},errors:b.keepErrors?d.errors:{},isSubmitSuccessful:!!b.keepIsSubmitSuccessful&&d.isSubmitSuccessful,isSubmitting:!1,defaultValues:f})},aq=(a,b)=>ap(L(a)?a(g):a,b),ar=a=>{d={...d,...a}},as={control:{register:am,unregister:ak,getFieldState:af,handleSubmit:ao,setError:ag,_subscribe:ah,_runSchema:D,_focusError:an,_getWatch:P,_getDirty:H,_setValid:o,_setFieldArray:(a,b=[],i,j,n=!0,o=!0)=>{if(j&&i&&!c.disabled){if(h.action=!0,o&&Array.isArray(z(e,a))){let b=i(z(e,a),j.argA,j.argB);n&&A(e,a,b)}if(o&&Array.isArray(z(d.errors,a))){let b,c=i(z(d.errors,a),j.argA,j.argB);n&&A(d.errors,a,c),x(z(b=d.errors,a)).length||O(b,a)}if((k.touchedFields||l.touchedFields)&&o&&Array.isArray(z(d.touchedFields,a))){let b=i(z(d.touchedFields,a),j.argA,j.argB);n&&A(d.touchedFields,a,b)}(k.dirtyFields||l.dirtyFields)&&(d.dirtyFields=R(f,g)),m.state.next({name:a,isDirty:H(a,b),dirtyFields:d.dirtyFields,errors:d.errors,isValid:d.isValid})}else A(g,a,b)},_setDisabledField:al,_setErrors:a=>{d.errors=a,m.state.next({errors:d.errors,isValid:!1})},_getFieldArray:a=>x(z(h.mount?g:f,a,c.shouldUnregister?z(f,a,[]):[])),_reset:ap,_resetDefaultValues:()=>L(c.defaultValues)&&c.defaultValues().then(a=>{aq(a,c.resetOptions),m.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let a of i.unMount){let b=z(e,a);b&&(b._f.refs?b._f.refs.every(a=>!N(a)):!N(b._f.ref))&&ak(a)}i.unMount=new Set},_disableForm:a=>{"boolean"==typeof a&&(m.state.next({disabled:a}),ac(e,(b,c)=>{let d=z(e,c);d&&(b.disabled=d._f.disabled||a,Array.isArray(d._f.refs)&&d._f.refs.forEach(b=>{b.disabled=d._f.disabled||a}))},0,!1))},_subjects:m,_proxyFormState:k,get _fields(){return e},get _formValues(){return g},get _state(){return h},set _state(value){h=value},get _defaultValues(){return f},get _names(){return i},set _names(value){i=value},get _formState(){return d},get _options(){return c},set _options(value){c={...c,...value}}},subscribe:a=>(h.mount=!0,l={...l,...a.formState},ah({...a,formState:l})),trigger:X,register:am,handleSubmit:ao,watch:(a,b)=>L(a)?m.state.subscribe({next:c=>"values"in c&&a(P(void 0,b),c)}):P(a,b,!0),setValue:T,getValues:_,reset:aq,resetField:(a,b={})=>{z(e,a)&&(w(b.defaultValue)?T(a,u(z(f,a))):(T(a,b.defaultValue),A(f,a,u(b.defaultValue))),b.keepTouched||O(d.touchedFields,a),b.keepDirty||(O(d.dirtyFields,a),d.isDirty=b.defaultValue?H(a,u(z(f,a))):H()),!b.keepError&&(O(d.errors,a),k.isValid&&o()),m.state.next({...d}))},clearErrors:a=>{a&&I(a).forEach(a=>O(d.errors,a)),m.state.next({errors:a?d.errors:{}})},unregister:ak,setError:ag,setFocus:(a,b={})=>{let c=z(e,a),d=c&&c._f;if(d){let a=d.refs?d.refs[0]:d.ref;a.focus&&(a.focus(),b.shouldSelect&&L(a.select)&&a.select())}},getFieldState:af};return{...as,formControl:as}}(a);b.current={...e,formState:d}}let g=b.current.control;return g._options=a,E(()=>{let a=g._subscribe({formState:g._proxyFormState,callback:()=>f({...g._formState}),reRenderRoot:!0});return f(a=>({...a,isReady:!0})),g._formState.isReady=!0,a},[g]),e.useEffect(()=>g._disableForm(a.disabled),[g,a.disabled]),e.useEffect(()=>{a.mode&&(g._options.mode=a.mode),a.reValidateMode&&(g._options.reValidateMode=a.reValidateMode)},[g,a.mode,a.reValidateMode]),e.useEffect(()=>{a.errors&&(g._setErrors(a.errors),g._focusError())},[g,a.errors]),e.useEffect(()=>{a.shouldUnregister&&g._subjects.state.next({values:g._getWatch()})},[g,a.shouldUnregister]),e.useEffect(()=>{if(g._proxyFormState.isDirty){let a=g._getDirty();a!==d.isDirty&&g._subjects.state.next({isDirty:a})}},[g,d.isDirty]),e.useEffect(()=>{a.values&&!G(a.values,c.current)?(g._reset(a.values,{keepFieldsRef:!0,...g._options.resetOptions}),c.current=a.values,f(a=>({...a}))):g._resetDefaultValues()},[g,a.values]),e.useEffect(()=>{g._state.mount||(g._setValid(),g._state.mount=!0),g._state.watch&&(g._state.watch=!1,g._subjects.state.next({...g._formState})),g._removeUnmounted()}),b.current.formState=((a,b,c,d=!0)=>{let e={defaultValues:b._defaultValues};for(let c in a)Object.defineProperty(e,c,{get:()=>(b._proxyFormState[c]!==C.all&&(b._proxyFormState[c]=!d||C.all),a[c])});return e})(d,g),b.current}(),X=T("email");T("password");let _=async()=>{if(!X)return void ak.o.error("Please enter your email first");o(!0);try{let a=await fetch("/api/auth/generate-verification-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:X,type:"registration"})}),b=await a.json();a.ok?(y(!0),F(b.code),W("verificationCode",b.code),ak.o.success("Verification code generated! (Auto-filled for development)")):ak.o.error(b.message||"Failed to generate verification code")}catch(a){ak.o.error("Failed to generate verification code")}finally{o(!1)}},af=async a=>{if(a.password!==a.confirmPassword)return void ak.o.error("Passwords do not match");if(!a.agreeToTerms)return void ak.o.error("Please agree to the Terms of Service and Privacy Policy");P(!0);try{let b=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firstName:a.firstName,lastName:a.lastName,email:a.email,password:a.password,verificationCode:a.verificationCode})}),c=await b.json();b.ok?(ak.o.success("Account created successfully!"),window.location.href="/auth/login"):ak.o.error(c.message||"Failed to create account")}catch(a){ak.o.error("Failed to create account")}finally{P(!1)}};return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-muted/50 px-4",children:(0,d.jsxs)("div",{className:"w-full max-w-md",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,d.jsx)(h,{className:"h-8 w-8 mr-2"}),(0,d.jsx)("span",{className:"text-2xl font-bold",children:"CanSell"})]}),(0,d.jsx)("h1",{className:"text-2xl font-bold",children:"Create your account"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Join our marketplace community today"})]}),(0,d.jsxs)(Object(function(){var a=Error("Cannot find module '@/components/ui/card'");throw a.code="MODULE_NOT_FOUND",a}()),{children:[(0,d.jsxs)(Object(function(){var a=Error("Cannot find module '@/components/ui/card'");throw a.code="MODULE_NOT_FOUND",a}()),{children:[(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/ui/card'");throw a.code="MODULE_NOT_FOUND",a}()),{children:"Sign Up"}),(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/ui/card'");throw a.code="MODULE_NOT_FOUND",a}()),{children:"Create your account to start buying and selling"})]}),(0,d.jsxs)(Object(function(){var a=Error("Cannot find module '@/components/ui/card'");throw a.code="MODULE_NOT_FOUND",a}()),{children:[(0,d.jsxs)("form",{onSubmit:S(af),className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"First Name"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(i,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/ui/input'");throw a.code="MODULE_NOT_FOUND",a}()),{...Q("firstName",{required:"First name is required"}),placeholder:"John",className:"pl-10"})]}),U.firstName&&(0,d.jsx)("p",{className:"text-sm text-red-500 mt-1",children:U.firstName.message})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Last Name"}),(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/ui/input'");throw a.code="MODULE_NOT_FOUND",a}()),{...Q("lastName",{required:"Last name is required"}),placeholder:"Doe"}),U.lastName&&(0,d.jsx)("p",{className:"text-sm text-red-500 mt-1",children:U.lastName.message})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Email"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(j,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/ui/input'");throw a.code="MODULE_NOT_FOUND",a}()),{...Q("email",{required:"Email is required",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Invalid email address"}}),type:"email",placeholder:"<EMAIL>",className:"pl-10"})]}),U.email&&(0,d.jsx)("p",{className:"text-sm text-red-500 mt-1",children:U.email.message})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Password"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(k,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/ui/input'");throw a.code="MODULE_NOT_FOUND",a}()),{...Q("password",{required:"Password is required",minLength:{value:8,message:"Password must be at least 8 characters"}}),type:a?"text":"password",placeholder:"Create a strong password",className:"pl-10 pr-10"}),(0,d.jsx)("button",{type:"button",onClick:()=>b(!a),className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:a?(0,d.jsx)(l,{className:"h-4 w-4 text-muted-foreground"}):(0,d.jsx)(m,{className:"h-4 w-4 text-muted-foreground"})})]}),U.password&&(0,d.jsx)("p",{className:"text-sm text-red-500 mt-1",children:U.password.message}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Must be at least 8 characters with numbers and letters"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Confirm Password"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(k,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/ui/input'");throw a.code="MODULE_NOT_FOUND",a}()),{...Q("confirmPassword",{required:"Please confirm your password"}),type:c?"text":"password",placeholder:"Confirm your password",className:"pl-10 pr-10"}),(0,d.jsx)("button",{type:"button",onClick:()=>f(!c),className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:c?(0,d.jsx)(l,{className:"h-4 w-4 text-muted-foreground"}):(0,d.jsx)(m,{className:"h-4 w-4 text-muted-foreground"})})]}),U.confirmPassword&&(0,d.jsx)("p",{className:"text-sm text-red-500 mt-1",children:U.confirmPassword.message})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Verification Code"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsxs)("div",{className:"relative flex-1",children:[(0,d.jsx)(n,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/ui/input'");throw a.code="MODULE_NOT_FOUND",a}()),{...Q("verificationCode",{required:"Verification code is required",pattern:{value:/^\d{6}$/,message:"Verification code must be 6 digits"}}),placeholder:"Enter 6-digit code",className:"pl-10",maxLength:6})]}),(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/ui/button'");throw a.code="MODULE_NOT_FOUND",a}()),{type:"button",variant:"outline",onClick:_,disabled:!X||g,className:"whitespace-nowrap",children:g?"Generating...":"Get Code"})]}),U.verificationCode&&(0,d.jsx)("p",{className:"text-sm text-red-500",children:U.verificationCode.message}),v&&D&&(0,d.jsxs)("div",{className:"p-3 bg-green-50 border border-green-200 rounded-md",children:[(0,d.jsxs)("p",{className:"text-sm text-green-800",children:[(0,d.jsx)("strong",{children:"Development Mode:"})," Your verification code is:"," ",(0,d.jsx)("code",{className:"font-mono font-bold",children:D})]}),(0,d.jsx)("p",{className:"text-xs text-green-600 mt-1",children:"In production, this would be sent via email/SMS"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("label",{className:"flex items-start space-x-2",children:[(0,d.jsx)("input",{...Q("agreeToTerms",{required:!0}),type:"checkbox",className:"mt-1"}),(0,d.jsxs)("span",{className:"text-sm",children:["I agree to the"," ",(0,d.jsx)(p(),{href:"/terms",className:"text-primary hover:underline",children:"Terms of Service"})," ","and"," ",(0,d.jsx)(p(),{href:"/privacy",className:"text-primary hover:underline",children:"Privacy Policy"})]})]}),U.agreeToTerms&&(0,d.jsx)("p",{className:"text-sm text-red-500",children:"You must agree to the terms and privacy policy"}),(0,d.jsxs)("label",{className:"flex items-start space-x-2",children:[(0,d.jsx)("input",{...Q("marketingEmails"),type:"checkbox",className:"mt-1"}),(0,d.jsx)("span",{className:"text-sm",children:"I want to receive marketing emails about new features and promotions"})]})]}),(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/ui/button'");throw a.code="MODULE_NOT_FOUND",a}()),{type:"submit",className:"w-full",disabled:H,children:H?"Creating Account...":"Create Account"})]}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,d.jsx)("span",{className:"w-full border-t"})}),(0,d.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,d.jsx)("span",{className:"bg-background px-2 text-muted-foreground",children:"Or continue with"})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)(Object(function(){var a=Error("Cannot find module '@/components/ui/button'");throw a.code="MODULE_NOT_FOUND",a}()),{variant:"outline",children:[(0,d.jsxs)("svg",{className:"h-4 w-4 mr-2",viewBox:"0 0 24 24",children:[(0,d.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,d.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,d.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,d.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Google"]}),(0,d.jsxs)(Object(function(){var a=Error("Cannot find module '@/components/ui/button'");throw a.code="MODULE_NOT_FOUND",a}()),{variant:"outline",children:[(0,d.jsx)("svg",{className:"h-4 w-4 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),"Facebook"]})]})]})]}),(0,d.jsxs)("p",{className:"text-center text-sm text-muted-foreground mt-6",children:["Already have an account?"," ",(0,d.jsx)(p(),{href:"/auth/login",className:"text-primary hover:underline",children:"Sign in"})]})]})})}!function(){var a=Error("Cannot find module '@/components/ui/button'");throw a.code="MODULE_NOT_FOUND",a}(),function(){var a=Error("Cannot find module '@/components/ui/card'");throw a.code="MODULE_NOT_FOUND",a}(),function(){var a=Error("Cannot find module '@/components/ui/input'");throw a.code="MODULE_NOT_FOUND",a}()},8354:a=>{"use strict";a.exports=require("util")},8535:(a,b,c)=>{Promise.resolve().then(c.bind(c,8577))},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[131,991,53],()=>b(b.s=119));module.exports=c})();