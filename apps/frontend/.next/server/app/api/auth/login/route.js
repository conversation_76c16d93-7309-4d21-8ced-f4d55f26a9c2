const CHUNK_PUBLIC_PATH = "server/app/api/auth/login/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/2c0cf_next_945507d1._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__af6ff701._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/frontend/.next-internal/server/app/api/auth/login/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.4.6_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/frontend/src/app/api/auth/login/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.4.6_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/frontend/src/app/api/auth/login/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
