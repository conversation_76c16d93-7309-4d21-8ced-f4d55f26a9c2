import { PrismaService } from '../../database/prisma.service';
import { GenerateVerificationCodeDto, VerifyCodeDto } from './dto/verify-code.dto';
export declare class VerificationService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    generateVerificationCode(dto: GenerateVerificationCodeDto): Promise<{
        message: string;
        code: string;
        expiresAt: Date;
    }>;
    verifyCode(dto: VerifyCodeDto): Promise<boolean>;
    isCodeValid(email: string, code: string, type: string): Promise<boolean>;
    cleanupExpiredCodes(): Promise<void>;
}
