"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
const bcrypt = require("bcryptjs");
const prisma_service_1 = require("../../database/prisma.service");
const client_1 = require("@prisma/client");
const verification_service_1 = require("./verification.service");
let AuthService = class AuthService {
    constructor(jwtService, configService, prisma, verificationService) {
        this.jwtService = jwtService;
        this.configService = configService;
        this.prisma = prisma;
        this.verificationService = verificationService;
    }
    async validateUser(email, password) {
        const user = await this.findUserByEmail(email);
        if (user && (await bcrypt.compare(password, user.password))) {
            const { password: _, ...result } = user;
            return result;
        }
        return null;
    }
    async register(registerDto) {
        const existingUser = await this.findUserByEmail(registerDto.email);
        if (existingUser) {
            throw new common_1.ConflictException('User with this email already exists');
        }
        const isCodeValid = await this.verificationService.isCodeValid(registerDto.email, registerDto.verificationCode, 'registration');
        if (!isCodeValid) {
            throw new common_1.BadRequestException('Invalid or expired verification code');
        }
        await this.verificationService.verifyCode({
            email: registerDto.email,
            code: registerDto.verificationCode,
            type: 'registration',
        });
        const hashedPassword = await bcrypt.hash(registerDto.password, 12);
        const user = await this.createUser({
            ...registerDto,
            password: hashedPassword,
        });
        const { password: _, ...result } = user;
        const tokens = await this.generateTokens(result);
        return {
            user: result,
            ...tokens,
        };
    }
    async login(user) {
        await this.updateLastLogin(user.id);
        const tokens = await this.generateTokens(user);
        return {
            user,
            ...tokens,
        };
    }
    async refreshToken(user) {
        const tokens = await this.generateTokens(user);
        return tokens;
    }
    async logout(userId) {
        return { message: 'Successfully logged out' };
    }
    async generateTokens(user) {
        const payload = { email: user.email, sub: user.id, role: user.role };
        const [accessToken, refreshToken] = await Promise.all([
            this.jwtService.signAsync(payload),
            this.jwtService.signAsync(payload, {
                secret: this.configService.get('REFRESH_TOKEN_SECRET'),
                expiresIn: this.configService.get('REFRESH_TOKEN_EXPIRES_IN', '30d'),
            }),
        ]);
        return {
            accessToken,
            refreshToken,
            expiresIn: this.configService.get('JWT_EXPIRES_IN', '7d'),
        };
    }
    async findUserByEmail(email) {
        return await this.prisma.user.findUnique({
            where: { email },
            include: {
                sellerProfile: true,
                buyerProfile: true,
                location: true,
            },
        });
    }
    async createUser(userData) {
        const user = await this.prisma.user.create({
            data: {
                email: userData.email,
                username: userData.username || userData.email.split('@')[0],
                firstName: userData.firstName,
                lastName: userData.lastName,
                password: userData.password,
                role: userData.role || client_1.UserRole.BUYER,
                isVerified: false,
            },
            include: {
                sellerProfile: true,
                buyerProfile: true,
                location: true,
            },
        });
        if (user.role === client_1.UserRole.SELLER) {
            await this.prisma.sellerProfile.create({
                data: {
                    userId: user.id,
                },
            });
        }
        else if (user.role === client_1.UserRole.BUYER) {
            await this.prisma.buyerProfile.create({
                data: {
                    userId: user.id,
                },
            });
        }
        return user;
    }
    async updateLastLogin(userId) {
        await this.prisma.user.update({
            where: { id: userId },
            data: { lastLoginAt: new Date() },
        });
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        config_1.ConfigService,
        prisma_service_1.PrismaService,
        verification_service_1.VerificationService])
], AuthService);
//# sourceMappingURL=auth.service.js.map