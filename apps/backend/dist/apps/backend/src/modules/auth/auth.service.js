"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
const bcrypt = require("bcryptjs");
const verification_service_1 = require("./verification.service");
const tokenBlacklist = new Set();
let AuthService = class AuthService {
    constructor(jwtService, configService, verificationService) {
        this.jwtService = jwtService;
        this.configService = configService;
        this.verificationService = verificationService;
        this.users = new Map();
        this.initializeTestUsers();
    }
    async initializeTestUsers() {
        const testUsers = [
            {
                id: '1',
                email: '<EMAIL>',
                username: 'testuser',
                firstName: 'Test',
                lastName: 'User',
                password: 'password123',
                role: 'BUYER',
                isVerified: true,
            },
            {
                id: '2',
                email: '<EMAIL>',
                username: 'admin',
                firstName: 'Admin',
                lastName: 'User',
                password: 'admin123',
                role: 'ADMIN',
                isVerified: true,
            },
        ];
        for (const userData of testUsers) {
            const hashedPassword = await bcrypt.hash(userData.password, 12);
            const user = {
                ...userData,
                password: hashedPassword,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            this.users.set(user.email, user);
        }
    }
    async validateUser(email, password) {
        const user = this.users.get(email);
        if (!user) {
            return null;
        }
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            return null;
        }
        const { password: _, ...userWithoutPassword } = user;
        return userWithoutPassword;
    }
    async register(registerDto) {
        if (this.users.has(registerDto.email)) {
            throw new common_1.ConflictException('User with this email already exists');
        }
        const isCodeValid = await this.verificationService.isCodeValid(registerDto.email, registerDto.verificationCode, 'registration');
        if (!isCodeValid) {
            throw new common_1.BadRequestException('Invalid or expired verification code');
        }
        await this.verificationService.verifyCode({
            email: registerDto.email,
            code: registerDto.verificationCode,
            type: 'registration',
        });
        const hashedPassword = await bcrypt.hash(registerDto.password, 12);
        const user = {
            id: Math.random().toString(36).substr(2, 9),
            email: registerDto.email,
            username: registerDto.email.split('@')[0],
            firstName: registerDto.firstName,
            lastName: registerDto.lastName,
            password: hashedPassword,
            role: 'BUYER',
            isVerified: true,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        this.users.set(user.email, user);
        const { password: _, ...userWithoutPassword } = user;
        const tokens = await this.generateTokens(userWithoutPassword);
        return {
            user: userWithoutPassword,
            ...tokens,
        };
    }
    async login(user) {
        const storedUser = this.users.get(user.email);
        if (storedUser) {
            storedUser.lastLoginAt = new Date();
            storedUser.updatedAt = new Date();
            this.users.set(user.email, storedUser);
        }
        const tokens = await this.generateTokens(user);
        return {
            user,
            ...tokens,
        };
    }
    async refreshToken(refreshToken) {
        try {
            const payload = await this.jwtService.verifyAsync(refreshToken, {
                secret: this.configService.get('REFRESH_TOKEN_SECRET'),
            });
            if (tokenBlacklist.has(refreshToken)) {
                throw new common_1.UnauthorizedException('Token has been revoked');
            }
            const user = this.users.get(payload.email);
            if (!user) {
                throw new common_1.UnauthorizedException('User not found');
            }
            const { password: _, ...userWithoutPassword } = user;
            const tokens = await this.generateTokens(userWithoutPassword);
            tokenBlacklist.add(refreshToken);
            return tokens;
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid refresh token');
        }
    }
    async logout(refreshToken) {
        if (refreshToken) {
            tokenBlacklist.add(refreshToken);
        }
        return { message: 'Successfully logged out' };
    }
    async validateToken(token) {
        try {
            const payload = await this.jwtService.verifyAsync(token);
            const user = this.users.get(payload.email);
            if (!user) {
                throw new common_1.UnauthorizedException('User not found');
            }
            const { password: _, ...userWithoutPassword } = user;
            return userWithoutPassword;
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid token');
        }
    }
    async generateTokens(user) {
        const payload = { email: user.email, sub: user.id, role: user.role };
        const [accessToken, refreshToken] = await Promise.all([
            this.jwtService.signAsync(payload),
            this.jwtService.signAsync(payload, {
                secret: this.configService.get('REFRESH_TOKEN_SECRET'),
                expiresIn: this.configService.get('REFRESH_TOKEN_EXPIRES_IN', '30d'),
            }),
        ]);
        return {
            accessToken,
            refreshToken,
            expiresIn: this.configService.get('JWT_EXPIRES_IN', '7d'),
        };
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        config_1.ConfigService,
        verification_service_1.VerificationService])
], AuthService);
//# sourceMappingURL=auth.service.js.map