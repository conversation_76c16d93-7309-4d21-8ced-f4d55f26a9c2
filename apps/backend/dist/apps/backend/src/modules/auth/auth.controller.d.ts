import { AuthService } from './auth.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { VerificationService } from './verification.service';
import { GenerateVerificationCodeDto, VerifyCodeDto } from './dto/verify-code.dto';
export declare class AuthController {
    private readonly authService;
    private readonly verificationService;
    constructor(authService: AuthService, verificationService: VerificationService);
    register(registerDto: RegisterDto): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: any;
        user: {
            id: string;
            email: string;
            username: string;
            firstName: string;
            lastName: string;
            role: string;
            isVerified: boolean;
            createdAt: Date;
            updatedAt: Date;
        };
    }>;
    login(req: any, loginDto: LoginDto): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: any;
        user: any;
    }>;
    getProfile(req: any): any;
    refresh(req: any): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: any;
    }>;
    logout(req: any): Promise<{
        message: string;
    }>;
    generateVerificationCode(dto: GenerateVerificationCodeDto): Promise<{
        message: string;
        code: string;
        expiresAt: Date;
    }>;
    verifyCode(dto: VerifyCodeDto): Promise<{
        valid: boolean;
        message: string;
    }>;
}
