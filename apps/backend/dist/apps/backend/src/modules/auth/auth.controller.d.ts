import { AuthService } from './auth.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { VerificationService } from './verification.service';
import { GenerateVerificationCodeDto, VerifyCodeDto } from './dto/verify-code.dto';
export declare class AuthController {
    private readonly authService;
    private readonly verificationService;
    constructor(authService: AuthService, verificationService: VerificationService);
    register(registerDto: RegisterDto): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: any;
        user: {
            sellerProfile: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                isVerified: boolean;
                businessName: string | null;
                businessType: string | null;
                taxId: string | null;
                bankAccount: string | null;
                paypalEmail: string | null;
                totalSales: number;
                averageRating: number;
                totalReviews: number;
                verifiedAt: Date | null;
                userId: string;
            };
            buyerProfile: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                userId: string;
                preferredCategories: string[];
                maxBudget: number | null;
                currency: string;
            };
            location: {
                id: string;
                type: string;
                createdAt: Date;
                updatedAt: Date;
                name: string;
                parentId: string | null;
                code: string | null;
                latitude: number | null;
                longitude: number | null;
            };
            id: string;
            createdAt: Date;
            updatedAt: Date;
            isActive: boolean;
            email: string;
            username: string;
            firstName: string;
            lastName: string;
            role: import("@prisma/client").$Enums.UserRole;
            isVerified: boolean;
            avatar: string | null;
            phone: string | null;
            dateOfBirth: Date | null;
            lastLoginAt: Date | null;
            locationId: string | null;
        };
    }>;
    login(req: any, loginDto: LoginDto): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: any;
        user: any;
    }>;
    getProfile(req: any): any;
    refresh(req: any): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: any;
    }>;
    logout(req: any): Promise<{
        message: string;
    }>;
    generateVerificationCode(dto: GenerateVerificationCodeDto): Promise<{
        message: string;
        code: string;
        expiresAt: Date;
    }>;
    verifyCode(dto: VerifyCodeDto): Promise<{
        valid: boolean;
        message: string;
    }>;
}
