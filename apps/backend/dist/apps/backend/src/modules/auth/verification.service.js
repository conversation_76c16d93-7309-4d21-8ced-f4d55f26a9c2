"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerificationService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../database/prisma.service");
let VerificationService = class VerificationService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async generateVerificationCode(dto) {
        const code = Math.floor(100000 + Math.random() * 900000).toString();
        const expiresAt = new Date();
        expiresAt.setMinutes(expiresAt.getMinutes() + 15);
        await this.prisma.verificationCode.updateMany({
            where: {
                email: dto.email,
                type: dto.type,
                isUsed: false,
            },
            data: {
                isUsed: true,
                usedAt: new Date(),
            },
        });
        const verificationCode = await this.prisma.verificationCode.create({
            data: {
                email: dto.email,
                code,
                type: dto.type,
                expiresAt,
            },
        });
        return {
            message: 'Verification code generated successfully',
            code: code,
            expiresAt,
        };
    }
    async verifyCode(dto) {
        const verificationCode = await this.prisma.verificationCode.findFirst({
            where: {
                email: dto.email,
                code: dto.code,
                type: dto.type,
                isUsed: false,
            },
        });
        if (!verificationCode) {
            throw new common_1.BadRequestException('Invalid or expired verification code');
        }
        if (new Date() > verificationCode.expiresAt) {
            throw new common_1.BadRequestException('Verification code has expired');
        }
        await this.prisma.verificationCode.update({
            where: { id: verificationCode.id },
            data: {
                isUsed: true,
                usedAt: new Date(),
            },
        });
        return true;
    }
    async isCodeValid(email, code, type) {
        const verificationCode = await this.prisma.verificationCode.findFirst({
            where: {
                email,
                code,
                type,
                isUsed: false,
            },
        });
        if (!verificationCode) {
            return false;
        }
        if (new Date() > verificationCode.expiresAt) {
            return false;
        }
        return true;
    }
    async cleanupExpiredCodes() {
        const oneDayAgo = new Date();
        oneDayAgo.setHours(oneDayAgo.getHours() - 24);
        await this.prisma.verificationCode.deleteMany({
            where: {
                OR: [
                    { expiresAt: { lt: new Date() } },
                    { createdAt: { lt: oneDayAgo } },
                ],
            },
        });
    }
};
exports.VerificationService = VerificationService;
exports.VerificationService = VerificationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], VerificationService);
//# sourceMappingURL=verification.service.js.map