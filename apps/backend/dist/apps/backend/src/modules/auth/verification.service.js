"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerificationService = void 0;
const common_1 = require("@nestjs/common");
let VerificationService = class VerificationService {
    constructor() {
        this.codes = new Map();
    }
    async generateVerificationCode(dto) {
        const code = Math.floor(100000 + Math.random() * 900000).toString();
        const expiresAt = new Date();
        expiresAt.setMinutes(expiresAt.getMinutes() + 15);
        const key = `${dto.email}:${dto.type}`;
        this.codes.set(key, {
            code,
            expiresAt,
            isUsed: false,
        });
        return {
            message: 'Verification code generated successfully',
            code: code,
            expiresAt,
        };
    }
    async verifyCode(dto) {
        const key = `${dto.email}:${dto.type}`;
        const verificationCode = this.codes.get(key);
        if (!verificationCode) {
            throw new common_1.BadRequestException('Invalid or expired verification code');
        }
        if (verificationCode.code !== dto.code) {
            throw new common_1.BadRequestException('Invalid verification code');
        }
        if (verificationCode.isUsed) {
            throw new common_1.BadRequestException('Verification code has already been used');
        }
        if (new Date() > verificationCode.expiresAt) {
            throw new common_1.BadRequestException('Verification code has expired');
        }
        verificationCode.isUsed = true;
        return true;
    }
    async isCodeValid(email, code, type) {
        const key = `${email}:${type}`;
        const verificationCode = this.codes.get(key);
        if (!verificationCode) {
            return false;
        }
        if (verificationCode.code !== code) {
            return false;
        }
        if (verificationCode.isUsed) {
            return false;
        }
        if (new Date() > verificationCode.expiresAt) {
            return false;
        }
        return true;
    }
    async cleanupExpiredCodes() {
        const now = new Date();
        for (const [key, value] of this.codes.entries()) {
            if (now > value.expiresAt) {
                this.codes.delete(key);
            }
        }
    }
};
exports.VerificationService = VerificationService;
exports.VerificationService = VerificationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], VerificationService);
//# sourceMappingURL=verification.service.js.map