import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../database/prisma.service';
import { RegisterDto } from './dto/register.dto';
import { VerificationService } from './verification.service';
export declare class AuthService {
    private readonly jwtService;
    private readonly configService;
    private readonly prisma;
    private readonly verificationService;
    constructor(jwtService: JwtService, configService: ConfigService, prisma: PrismaService, verificationService: VerificationService);
    validateUser(email: string, password: string): Promise<any>;
    register(registerDto: RegisterDto): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: any;
        user: {
            sellerProfile: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                isVerified: boolean;
                businessName: string | null;
                businessType: string | null;
                taxId: string | null;
                bankAccount: string | null;
                paypalEmail: string | null;
                totalSales: number;
                averageRating: number;
                totalReviews: number;
                verifiedAt: Date | null;
                userId: string;
            };
            buyerProfile: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                userId: string;
                preferredCategories: string[];
                maxBudget: number | null;
                currency: string;
            };
            location: {
                id: string;
                type: string;
                createdAt: Date;
                updatedAt: Date;
                name: string;
                parentId: string | null;
                code: string | null;
                latitude: number | null;
                longitude: number | null;
            };
            id: string;
            createdAt: Date;
            updatedAt: Date;
            isActive: boolean;
            email: string;
            username: string;
            firstName: string;
            lastName: string;
            role: import("@prisma/client").$Enums.UserRole;
            isVerified: boolean;
            avatar: string | null;
            phone: string | null;
            dateOfBirth: Date | null;
            lastLoginAt: Date | null;
            locationId: string | null;
        };
    }>;
    login(user: any): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: any;
        user: any;
    }>;
    refreshToken(user: any): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: any;
    }>;
    logout(userId: string): Promise<{
        message: string;
    }>;
    private generateTokens;
    private findUserByEmail;
    private createUser;
    updateLastLogin(userId: string): Promise<void>;
}
