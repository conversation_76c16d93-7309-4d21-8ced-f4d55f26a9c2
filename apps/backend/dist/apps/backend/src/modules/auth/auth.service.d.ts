import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { RegisterDto } from './dto/register.dto';
import { VerificationService } from './verification.service';
export declare class AuthService {
    private readonly jwtService;
    private readonly configService;
    private readonly verificationService;
    private users;
    constructor(jwtService: JwtService, configService: ConfigService, verificationService: VerificationService);
    private initializeTestUsers;
    validateUser(email: string, password: string): Promise<any>;
    register(registerDto: RegisterDto): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: any;
        user: {
            id: string;
            email: string;
            username: string;
            firstName: string;
            lastName: string;
            role: "BUYER" | "SELLER" | "ADMIN";
            isVerified: boolean;
            createdAt: Date;
            updatedAt: Date;
            lastLoginAt?: Date;
        };
    }>;
    login(user: any): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: any;
        user: any;
    }>;
    refreshToken(refreshToken: string): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: any;
    }>;
    logout(refreshToken?: string): Promise<{
        message: string;
    }>;
    validateToken(token: string): Promise<any>;
    private generateTokens;
}
