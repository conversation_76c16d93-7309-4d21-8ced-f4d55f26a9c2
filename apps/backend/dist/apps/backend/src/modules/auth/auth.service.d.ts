import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { RegisterDto } from './dto/register.dto';
import { VerificationService } from './verification.service';
export declare class AuthService {
    private readonly jwtService;
    private readonly configService;
    private readonly verificationService;
    constructor(jwtService: JwtService, configService: ConfigService, verificationService: VerificationService);
    validateUser(email: string, password: string): Promise<any>;
    register(registerDto: RegisterDto): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: any;
        user: {
            id: string;
            email: string;
            username: string;
            firstName: string;
            lastName: string;
            role: string;
            isVerified: boolean;
            createdAt: Date;
            updatedAt: Date;
        };
    }>;
    login(user: any): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: any;
        user: any;
    }>;
    refreshToken(user: any): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: any;
    }>;
    logout(userId: string): Promise<{
        message: string;
    }>;
    private generateTokens;
}
