"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationService = void 0;
const common_1 = require("@nestjs/common");
let LocationService = class LocationService {
    async getCountries() {
        return [
            {
                id: '1',
                name: 'United States',
                code: 'US',
                states: [],
            },
            {
                id: '2',
                name: 'Canada',
                code: 'CA',
                states: [],
            },
        ];
    }
    async getStatesByCountry(countryId) {
        if (countryId === '1') {
            return [
                {
                    id: '1',
                    name: 'California',
                    code: 'CA',
                    countryId: '1',
                    cities: [],
                },
                {
                    id: '2',
                    name: 'New York',
                    code: 'NY',
                    countryId: '1',
                    cities: [],
                },
            ];
        }
        return [];
    }
    async getCitiesByState(stateId) {
        if (stateId === '1') {
            return [
                {
                    id: '1',
                    name: 'Los Angeles',
                    stateId: '1',
                    latitude: 34.0522,
                    longitude: -118.2437,
                },
                {
                    id: '2',
                    name: 'San Francisco',
                    stateId: '1',
                    latitude: 37.7749,
                    longitude: -122.4194,
                },
            ];
        }
        return [];
    }
};
exports.LocationService = LocationService;
exports.LocationService = LocationService = __decorate([
    (0, common_1.Injectable)()
], LocationService);
//# sourceMappingURL=location.service.js.map