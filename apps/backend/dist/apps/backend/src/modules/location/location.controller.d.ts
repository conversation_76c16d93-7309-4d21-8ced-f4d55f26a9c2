import { LocationService } from './location.service';
export declare class LocationController {
    private readonly locationService;
    constructor(locationService: LocationService);
    getCountries(): Promise<import("@can-sell/types").Country[]>;
    getStatesByCountry(countryId: string): Promise<import("@can-sell/types").State[]>;
    getCitiesByState(stateId: string): Promise<import("@can-sell/types").City[]>;
}
