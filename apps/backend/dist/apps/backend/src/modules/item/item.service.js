"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ItemService = void 0;
const common_1 = require("@nestjs/common");
const types_1 = require("@can-sell/types");
let ItemService = class ItemService {
    constructor() {
        this.mockItems = [
            {
                id: '1',
                title: 'iPhone 15 Pro Max',
                description: 'Brand new iPhone 15 Pro Max in excellent condition',
                price: 1199,
                currency: 'USD',
                condition: types_1.ItemCondition.NEW,
                categoryId: '2',
                templateId: '1',
                sellerId: '1',
                locationId: '1',
                images: [],
                customFields: {
                    brand: 'Apple',
                    model: 'iPhone 15 Pro Max',
                    storage: '256GB',
                    color: 'Natural Titanium',
                },
                status: types_1.ItemStatus.ACTIVE,
                views: 0,
                favorites: 0,
                isPromoted: false,
                createdAt: new Date(),
                updatedAt: new Date(),
            },
        ];
    }
    async getItems(filters) {
        return {
            items: this.mockItems,
            total: this.mockItems.length,
            page: filters.page,
            limit: filters.limit,
            hasMore: false,
        };
    }
    async getItemById(id) {
        const item = this.mockItems.find(item => item.id === id);
        if (!item) {
            throw new common_1.NotFoundException(`Item with ID ${id} not found`);
        }
        return item;
    }
    async createItem(createItemDto) {
        const newItem = {
            id: Date.now().toString(),
            ...createItemDto,
            status: types_1.ItemStatus.PENDING_APPROVAL,
            views: 0,
            favorites: 0,
            isPromoted: false,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        this.mockItems.push(newItem);
        return newItem;
    }
};
exports.ItemService = ItemService;
exports.ItemService = ItemService = __decorate([
    (0, common_1.Injectable)()
], ItemService);
//# sourceMappingURL=item.service.js.map