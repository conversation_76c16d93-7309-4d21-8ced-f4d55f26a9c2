import { ItemService } from './item.service';
export declare class ItemController {
    private readonly itemService;
    constructor(itemService: ItemService);
    getItems(page?: number, limit?: number, category?: string, search?: string): Promise<{
        items: import("@can-sell/types").Item[];
        total: number;
        page: any;
        limit: any;
        hasMore: boolean;
    }>;
    getItemById(id: string): Promise<import("@can-sell/types").Item>;
    createItem(createItemDto: any): Promise<import("@can-sell/types").Item>;
}
