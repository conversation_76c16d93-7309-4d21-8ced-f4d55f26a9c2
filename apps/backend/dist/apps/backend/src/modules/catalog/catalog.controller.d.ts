import { CatalogService } from './catalog.service';
export declare class CatalogController {
    private readonly catalogService;
    constructor(catalogService: CatalogService);
    getCategories(parentId?: string): Promise<import("../../schemas/category.schema").Category[]>;
    getCategoryById(id: string): Promise<import("mongoose").Document<unknown, {}, import("../../schemas/category.schema").CategoryDocument, {}, {}> & import("../../schemas/category.schema").Category & import("mongoose").Document<unknown, any, any, Record<string, any>, {}> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    getTemplatesByCategory(categoryId: string): Promise<(import("mongoose").Document<unknown, {}, import("../../schemas/template.schema").TemplateDocument, {}, {}> & import("../../schemas/template.schema").Template & import("mongoose").Document<unknown, any, any, Record<string, any>, {}> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    })[]>;
    getTemplateById(id: string): Promise<import("mongoose").Document<unknown, {}, import("../../schemas/template.schema").TemplateDocument, {}, {}> & import("../../schemas/template.schema").Template & import("mongoose").Document<unknown, any, any, Record<string, any>, {}> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    createCategory(categoryData: any): Promise<import("mongoose").Document<unknown, {}, import("../../schemas/category.schema").CategoryDocument, {}, {}> & import("../../schemas/category.schema").Category & import("mongoose").Document<unknown, any, any, Record<string, any>, {}> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    updateCategory(id: string, updateData: any): Promise<import("mongoose").Document<unknown, {}, import("../../schemas/category.schema").CategoryDocument, {}, {}> & import("../../schemas/category.schema").Category & import("mongoose").Document<unknown, any, any, Record<string, any>, {}> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    deleteCategory(id: string): Promise<{
        message: string;
    }>;
    createTemplate(templateData: any): Promise<import("mongoose").Document<unknown, {}, import("../../schemas/template.schema").TemplateDocument, {}, {}> & import("../../schemas/template.schema").Template & import("mongoose").Document<unknown, any, any, Record<string, any>, {}> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    updateTemplate(id: string, updateData: any): Promise<import("mongoose").Document<unknown, {}, import("../../schemas/template.schema").TemplateDocument, {}, {}> & import("../../schemas/template.schema").Template & import("mongoose").Document<unknown, any, any, Record<string, any>, {}> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    deleteTemplate(id: string): Promise<{
        message: string;
    }>;
}
