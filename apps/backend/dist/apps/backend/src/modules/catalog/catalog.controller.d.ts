import { CatalogService } from './catalog.service';
export declare class CatalogController {
    private readonly catalogService;
    constructor(catalogService: CatalogService);
    getCategories(parentId?: string): Promise<any>;
    getCategoryById(id: string): Promise<any>;
    getTemplatesByCategory(categoryId: string): Promise<any>;
    getTemplateById(id: string): Promise<any>;
    createCategory(categoryData: any): Promise<any>;
    updateCategory(id: string, updateData: any): Promise<any>;
    deleteCategory(id: string): Promise<any>;
    createTemplate(templateData: any): Promise<any>;
    updateTemplate(id: string, updateData: any): Promise<any>;
    deleteTemplate(id: string): Promise<any>;
}
