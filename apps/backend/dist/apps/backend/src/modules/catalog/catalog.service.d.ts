import { Model } from 'mongoose';
import { Category, CategoryDocument } from '../../schemas/category.schema';
import { Template, TemplateDocument } from '../../schemas/template.schema';
export declare class CatalogService {
    private categoryModel;
    private templateModel;
    constructor(categoryModel: Model<CategoryDocument>, templateModel: Model<TemplateDocument>);
    getCategoryBySlug(slug: string): Promise<import("mongoose").Document<unknown, {}, CategoryDocument, {}, {}> & Category & import("mongoose").Document<unknown, any, any, Record<string, any>, {}> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    createCategory(categoryData: any): Promise<import("mongoose").Document<unknown, {}, CategoryDocument, {}, {}> & Category & import("mongoose").Document<unknown, any, any, Record<string, any>, {}> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    updateCategory(id: string, updateData: any): Promise<import("mongoose").Document<unknown, {}, CategoryDocument, {}, {}> & Category & import("mongoose").Document<unknown, any, any, Record<string, any>, {}> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    deleteCategory(id: string): Promise<{
        message: string;
    }>;
    getTemplates(): Promise<(import("mongoose").Document<unknown, {}, TemplateDocument, {}, {}> & Template & import("mongoose").Document<unknown, any, any, Record<string, any>, {}> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    })[]>;
    createTemplate(templateData: any): Promise<import("mongoose").Document<unknown, {}, TemplateDocument, {}, {}> & Template & import("mongoose").Document<unknown, any, any, Record<string, any>, {}> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    updateTemplate(id: string, updateData: any): Promise<import("mongoose").Document<unknown, {}, TemplateDocument, {}, {}> & Template & import("mongoose").Document<unknown, any, any, Record<string, any>, {}> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    deleteTemplate(id: string): Promise<{
        message: string;
    }>;
    private readonly mockCategories;
    private readonly mockTemplates;
}
