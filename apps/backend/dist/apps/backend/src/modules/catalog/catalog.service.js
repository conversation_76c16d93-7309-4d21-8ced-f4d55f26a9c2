"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CatalogService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const category_schema_1 = require("../../schemas/category.schema");
const template_schema_1 = require("../../schemas/template.schema");
let CatalogService = class CatalogService {
    constructor(categoryModel, templateModel) {
        this.categoryModel = categoryModel;
        this.templateModel = templateModel;
        this.mockCategories = [
            {
                id: '1',
                name: 'Electronics',
                slug: 'electronics',
                description: 'Electronic devices and gadgets',
                parentId: null,
                templateIds: ['1', '2'],
                isActive: true,
                sortOrder: 1,
                createdAt: new Date(),
                updatedAt: new Date(),
            },
            {
                id: '2',
                name: 'Smartphones',
                slug: 'smartphones',
                description: 'Mobile phones and accessories',
                parentId: '1',
                templateIds: ['1'],
                isActive: true,
                sortOrder: 1,
                createdAt: new Date(),
                updatedAt: new Date(),
            },
            {
                id: '3',
                name: 'Vehicles',
                slug: 'vehicles',
                description: 'Cars, motorcycles, and other vehicles',
                parentId: null,
                templateIds: ['3'],
                isActive: true,
                sortOrder: 2,
                createdAt: new Date(),
                updatedAt: new Date(),
            },
        ];
        this.mockTemplates = [
            {
                id: '1',
                name: 'Smartphone Template',
                description: 'Template for listing smartphones',
                categoryId: '2',
                fields: [
                    {
                        id: '1',
                        name: 'brand',
                        label: 'Brand',
                        type: FieldType.SELECT,
                        required: true,
                        options: ['Apple', 'Samsung', 'Google', 'OnePlus', 'Other'],
                        sortOrder: 1,
                    },
                    {
                        id: '2',
                        name: 'model',
                        label: 'Model',
                        type: FieldType.TEXT,
                        required: true,
                        placeholder: 'e.g., iPhone 15 Pro',
                        sortOrder: 2,
                    },
                    {
                        id: '3',
                        name: 'storage',
                        label: 'Storage Capacity',
                        type: FieldType.SELECT,
                        required: true,
                        options: ['64GB', '128GB', '256GB', '512GB', '1TB'],
                        sortOrder: 3,
                    },
                    {
                        id: '4',
                        name: 'color',
                        label: 'Color',
                        type: FieldType.TEXT,
                        required: false,
                        placeholder: 'e.g., Space Gray',
                        sortOrder: 4,
                    },
                ],
                isActive: true,
                version: 1,
                createdAt: new Date(),
                updatedAt: new Date(),
            },
        ];
    }
    async getCategories() {
        return await this.categoryModel
            .find({ isActive: true })
            .populate('subcategories')
            .sort({ sortOrder: 1, name: 1 })
            .exec();
    }
    async getCategoryById(id) {
        const category = await this.categoryModel
            .findById(id)
            .populate('subcategories')
            .populate('templates')
            .exec();
        if (!category) {
            throw new common_1.NotFoundException('Category not found');
        }
        return category;
    }
    async getCategoryBySlug(slug) {
        const category = await this.categoryModel
            .findOne({ slug, isActive: true })
            .populate('subcategories')
            .populate('templates')
            .exec();
        if (!category) {
            throw new common_1.NotFoundException('Category not found');
        }
        return category;
    }
    async createCategory(categoryData) {
        const category = new this.categoryModel(categoryData);
        return await category.save();
    }
    async updateCategory(id, updateData) {
        const category = await this.categoryModel
            .findByIdAndUpdate(id, updateData, { new: true })
            .exec();
        if (!category) {
            throw new common_1.NotFoundException('Category not found');
        }
        return category;
    }
    async deleteCategory(id) {
        const category = await this.categoryModel.findByIdAndDelete(id).exec();
        if (!category) {
            throw new common_1.NotFoundException('Category not found');
        }
        return { message: 'Category deleted successfully' };
    }
    async getTemplates() {
        return await this.templateModel
            .find({ isActive: true })
            .populate('category')
            .sort({ sortOrder: 1, name: 1 })
            .exec();
    }
    async getTemplateById(id) {
        const template = await this.templateModel
            .findById(id)
            .populate('category')
            .exec();
        if (!template) {
            throw new common_1.NotFoundException('Template not found');
        }
        return template;
    }
    async getTemplatesByCategory(categoryId) {
        return await this.templateModel
            .find({ categoryId, isActive: true })
            .sort({ sortOrder: 1, name: 1 })
            .exec();
    }
    async createTemplate(templateData) {
        const template = new this.templateModel(templateData);
        return await template.save();
    }
    async updateTemplate(id, updateData) {
        const template = await this.templateModel
            .findByIdAndUpdate(id, updateData, { new: true })
            .exec();
        if (!template) {
            throw new common_1.NotFoundException('Template not found');
        }
        return template;
    }
    async deleteTemplate(id) {
        const template = await this.templateModel.findByIdAndDelete(id).exec();
        if (!template) {
            throw new common_1.NotFoundException('Template not found');
        }
        return { message: 'Template deleted successfully' };
    }
    async getCategories(parentId) {
        if (parentId) {
            return this.mockCategories.filter((cat) => cat.parentId === parentId);
        }
        return this.mockCategories.filter((cat) => cat.parentId === null);
    }
    async getCategoryById(id) {
        const category = this.mockCategories.find((cat) => cat.id === id);
        if (!category) {
            throw new common_1.NotFoundException(`Category with ID ${id} not found`);
        }
        return category;
    }
    async getTemplatesByCategory(categoryId) {
        const category = await this.getCategoryById(categoryId);
        return this.mockTemplates.filter((template) => category.templateIds.includes(template.id));
    }
    async getTemplateById(id) {
        const template = this.mockTemplates.find((t) => t.id === id);
        if (!template) {
            throw new common_1.NotFoundException(`Template with ID ${id} not found`);
        }
        return template;
    }
};
exports.CatalogService = CatalogService;
exports.CatalogService = CatalogService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(category_schema_1.Category.name)),
    __param(1, (0, mongoose_1.InjectModel)(template_schema_1.Template.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], CatalogService);
//# sourceMappingURL=catalog.service.js.map