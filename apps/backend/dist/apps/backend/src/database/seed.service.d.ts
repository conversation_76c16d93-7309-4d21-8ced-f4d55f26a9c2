import { Model } from 'mongoose';
import { PrismaService } from './prisma.service';
import { CategoryDocument } from '../schemas/category.schema';
import { TemplateDocument } from '../schemas/template.schema';
export declare class SeedService {
    private readonly prisma;
    private categoryModel;
    private templateModel;
    private readonly logger;
    constructor(prisma: PrismaService, categoryModel: Model<CategoryDocument>, templateModel: Model<TemplateDocument>);
    seedAll(): Promise<void>;
    private seedLocations;
    private seedUsers;
    private seedCategories;
    private seedTemplates;
}
