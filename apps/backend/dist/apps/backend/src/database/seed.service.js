"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SeedService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeedService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const bcrypt = require("bcryptjs");
const prisma_service_1 = require("./prisma.service");
const category_schema_1 = require("../schemas/category.schema");
const template_schema_1 = require("../schemas/template.schema");
const client_1 = require("@prisma/client");
let SeedService = SeedService_1 = class SeedService {
    constructor(prisma, categoryModel, templateModel) {
        this.prisma = prisma;
        this.categoryModel = categoryModel;
        this.templateModel = templateModel;
        this.logger = new common_1.Logger(SeedService_1.name);
    }
    async seedAll() {
        this.logger.log('Starting database seeding...');
        try {
            await this.seedLocations();
            await this.seedUsers();
            await this.seedCategories();
            await this.seedTemplates();
            this.logger.log('Database seeding completed successfully!');
        }
        catch (error) {
            this.logger.error('Database seeding failed:', error);
            throw error;
        }
    }
    async seedLocations() {
        this.logger.log('Seeding locations...');
        const existingLocations = await this.prisma.location.count();
        if (existingLocations > 0) {
            this.logger.log('Locations already exist, skipping...');
            return;
        }
        const usa = await this.prisma.location.create({
            data: {
                name: 'United States',
                type: 'country',
                code: 'US',
            },
        });
        const canada = await this.prisma.location.create({
            data: {
                name: 'Canada',
                type: 'country',
                code: 'CA',
            },
        });
        const california = await this.prisma.location.create({
            data: {
                name: 'California',
                type: 'state',
                code: 'CA',
                parentId: usa.id,
            },
        });
        const newYork = await this.prisma.location.create({
            data: {
                name: 'New York',
                type: 'state',
                code: 'NY',
                parentId: usa.id,
            },
        });
        await this.prisma.location.createMany({
            data: [
                {
                    name: 'Los Angeles',
                    type: 'city',
                    parentId: california.id,
                    latitude: 34.0522,
                    longitude: -118.2437,
                },
                {
                    name: 'San Francisco',
                    type: 'city',
                    parentId: california.id,
                    latitude: 37.7749,
                    longitude: -122.4194,
                },
                {
                    name: 'New York City',
                    type: 'city',
                    parentId: newYork.id,
                    latitude: 40.7128,
                    longitude: -74.0060,
                },
            ],
        });
        this.logger.log('Locations seeded successfully');
    }
    async seedUsers() {
        this.logger.log('Seeding users...');
        const existingUsers = await this.prisma.user.count();
        if (existingUsers > 0) {
            this.logger.log('Users already exist, skipping...');
            return;
        }
        const hashedPassword = await bcrypt.hash('admin123!', 12);
        const adminUser = await this.prisma.user.create({
            data: {
                email: '<EMAIL>',
                username: 'admin',
                firstName: 'Admin',
                lastName: 'User',
                password: hashedPassword,
                role: client_1.UserRole.ADMIN,
                isVerified: true,
                isActive: true,
            },
        });
        const sellerPassword = await bcrypt.hash('seller123!', 12);
        const sellerUser = await this.prisma.user.create({
            data: {
                email: '<EMAIL>',
                username: 'testseller',
                firstName: 'Test',
                lastName: 'Seller',
                password: sellerPassword,
                role: client_1.UserRole.SELLER,
                isVerified: true,
                isActive: true,
            },
        });
        await this.prisma.sellerProfile.create({
            data: {
                userId: sellerUser.id,
                businessName: 'Test Electronics Store',
                businessType: 'Electronics',
                isVerified: true,
                verifiedAt: new Date(),
            },
        });
        const buyerPassword = await bcrypt.hash('buyer123!', 12);
        const buyerUser = await this.prisma.user.create({
            data: {
                email: '<EMAIL>',
                username: 'testbuyer',
                firstName: 'Test',
                lastName: 'Buyer',
                password: buyerPassword,
                role: client_1.UserRole.BUYER,
                isVerified: true,
                isActive: true,
            },
        });
        await this.prisma.buyerProfile.create({
            data: {
                userId: buyerUser.id,
                preferredCategories: ['electronics', 'smartphones'],
                maxBudget: 2000,
                currency: 'USD',
            },
        });
        this.logger.log('Users seeded successfully');
    }
    async seedCategories() {
        this.logger.log('Seeding categories...');
        const existingCategories = await this.categoryModel.countDocuments();
        if (existingCategories > 0) {
            this.logger.log('Categories already exist, skipping...');
            return;
        }
        const electronics = await this.categoryModel.create({
            name: 'Electronics',
            slug: 'electronics',
            description: 'Electronic devices and gadgets',
            isActive: true,
            sortOrder: 1,
            icon: 'smartphone',
        });
        const vehicles = await this.categoryModel.create({
            name: 'Vehicles',
            slug: 'vehicles',
            description: 'Cars, motorcycles, and other vehicles',
            isActive: true,
            sortOrder: 2,
            icon: 'car',
        });
        const fashion = await this.categoryModel.create({
            name: 'Fashion',
            slug: 'fashion',
            description: 'Clothing, shoes, and accessories',
            isActive: true,
            sortOrder: 3,
            icon: 'shirt',
        });
        await this.categoryModel.create({
            name: 'Smartphones',
            slug: 'smartphones',
            description: 'Mobile phones and accessories',
            parentId: electronics._id,
            isActive: true,
            sortOrder: 1,
        });
        await this.categoryModel.create({
            name: 'Laptops',
            slug: 'laptops',
            description: 'Laptops and notebooks',
            parentId: electronics._id,
            isActive: true,
            sortOrder: 2,
        });
        await this.categoryModel.create({
            name: 'Gaming',
            slug: 'gaming',
            description: 'Gaming consoles and accessories',
            parentId: electronics._id,
            isActive: true,
            sortOrder: 3,
        });
        this.logger.log('Categories seeded successfully');
    }
    async seedTemplates() {
        this.logger.log('Seeding templates...');
        const existingTemplates = await this.templateModel.countDocuments();
        if (existingTemplates > 0) {
            this.logger.log('Templates already exist, skipping...');
            return;
        }
        const smartphonesCategory = await this.categoryModel.findOne({ slug: 'smartphones' });
        const laptopsCategory = await this.categoryModel.findOne({ slug: 'laptops' });
        if (smartphonesCategory) {
            await this.templateModel.create({
                name: 'Smartphone Template',
                description: 'Template for listing smartphones',
                categoryId: smartphonesCategory._id,
                fields: [
                    {
                        id: 'brand',
                        name: 'brand',
                        label: 'Brand',
                        type: 'select',
                        required: true,
                        options: ['Apple', 'Samsung', 'Google', 'OnePlus', 'Xiaomi', 'Other'],
                        sortOrder: 1,
                    },
                    {
                        id: 'model',
                        name: 'model',
                        label: 'Model',
                        type: 'text',
                        required: true,
                        placeholder: 'e.g., iPhone 15 Pro',
                        sortOrder: 2,
                    },
                    {
                        id: 'storage',
                        name: 'storage',
                        label: 'Storage Capacity',
                        type: 'select',
                        required: true,
                        options: ['64GB', '128GB', '256GB', '512GB', '1TB'],
                        sortOrder: 3,
                    },
                    {
                        id: 'color',
                        name: 'color',
                        label: 'Color',
                        type: 'text',
                        required: false,
                        placeholder: 'e.g., Space Gray',
                        sortOrder: 4,
                    },
                ],
                isActive: true,
                sortOrder: 1,
            });
        }
        if (laptopsCategory) {
            await this.templateModel.create({
                name: 'Laptop Template',
                description: 'Template for listing laptops',
                categoryId: laptopsCategory._id,
                fields: [
                    {
                        id: 'brand',
                        name: 'brand',
                        label: 'Brand',
                        type: 'select',
                        required: true,
                        options: ['Apple', 'Dell', 'HP', 'Lenovo', 'ASUS', 'Acer', 'Other'],
                        sortOrder: 1,
                    },
                    {
                        id: 'processor',
                        name: 'processor',
                        label: 'Processor',
                        type: 'text',
                        required: true,
                        placeholder: 'e.g., Intel Core i7',
                        sortOrder: 2,
                    },
                    {
                        id: 'ram',
                        name: 'ram',
                        label: 'RAM',
                        type: 'select',
                        required: true,
                        options: ['4GB', '8GB', '16GB', '32GB', '64GB'],
                        sortOrder: 3,
                    },
                    {
                        id: 'storage',
                        name: 'storage',
                        label: 'Storage',
                        type: 'text',
                        required: true,
                        placeholder: 'e.g., 512GB SSD',
                        sortOrder: 4,
                    },
                ],
                isActive: true,
                sortOrder: 1,
            });
        }
        this.logger.log('Templates seeded successfully');
    }
};
exports.SeedService = SeedService;
exports.SeedService = SeedService = SeedService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, mongoose_1.InjectModel)(category_schema_1.Category.name)),
    __param(2, (0, mongoose_1.InjectModel)(template_schema_1.Template.name)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        mongoose_2.Model,
        mongoose_2.Model])
], SeedService);
//# sourceMappingURL=seed.service.js.map