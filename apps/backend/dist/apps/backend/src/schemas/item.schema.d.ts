import { Document, Types } from 'mongoose';
export type ItemDocument = Item & Document;
export declare enum ItemStatus {
    DRAFT = "draft",
    ACTIVE = "active",
    SOLD = "sold",
    EXPIRED = "expired",
    SUSPENDED = "suspended",
    DELETED = "deleted"
}
export declare enum ItemCondition {
    NEW = "new",
    LIKE_NEW = "like_new",
    EXCELLENT = "excellent",
    GOOD = "good",
    FAIR = "fair",
    POOR = "poor"
}
export declare class ItemImage {
    url: string;
    alt?: string;
    sortOrder: number;
    isPrimary: boolean;
}
export declare class Item {
    title: string;
    description?: string;
    price: number;
    currency: string;
    condition?: ItemCondition;
    categoryId: Types.ObjectId;
    templateId?: Types.ObjectId;
    sellerId: string;
    locationId?: string;
    images: ItemImage[];
    customFields?: Record<string, any>;
    status: ItemStatus;
    views: number;
    favorites: number;
    isPromoted: boolean;
    isFeatured: boolean;
    expiresAt?: Date;
    tags: string[];
    seoData?: {
        metaTitle?: string;
        metaDescription?: string;
        keywords?: string[];
    };
    analytics?: {
        impressions?: number;
        clicks?: number;
        contactRequests?: number;
    };
    category?: any;
    template?: any;
}
export declare const ItemImageSchema: import("mongoose").Schema<ItemImage, import("mongoose").Model<ItemImage, any, any, any, Document<unknown, any, ItemImage, any, {}> & ItemImage & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, ItemImage, Document<unknown, {}, import("mongoose").FlatRecord<ItemImage>, {}, import("mongoose").ResolveSchemaOptions<import("mongoose").DefaultSchemaOptions>> & import("mongoose").FlatRecord<ItemImage> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
export declare const ItemSchema: import("mongoose").Schema<Item, import("mongoose").Model<Item, any, any, any, Document<unknown, any, Item, any, {}> & Item & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Item, Document<unknown, {}, import("mongoose").FlatRecord<Item>, {}, import("mongoose").ResolveSchemaOptions<import("mongoose").DefaultSchemaOptions>> & import("mongoose").FlatRecord<Item> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
