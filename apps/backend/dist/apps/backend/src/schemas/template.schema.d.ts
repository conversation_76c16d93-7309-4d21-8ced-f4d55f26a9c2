import { Document, Types } from 'mongoose';
export type TemplateDocument = Template & Document;
export declare enum FieldType {
    TEXT = "text",
    TEXTAREA = "textarea",
    NUMBER = "number",
    SELECT = "select",
    MULTISELECT = "multiselect",
    CHECKBOX = "checkbox",
    RADIO = "radio",
    DATE = "date",
    EMAIL = "email",
    URL = "url",
    FILE = "file",
    IMAGE = "image"
}
export declare class TemplateField {
    id: string;
    name: string;
    label: string;
    type: FieldType;
    required: boolean;
    placeholder?: string;
    helpText?: string;
    options: string[];
    validation?: Record<string, any>;
    sortOrder: number;
    isActive: boolean;
}
export declare class Template {
    name: string;
    description?: string;
    categoryId: Types.ObjectId;
    fields: TemplateField[];
    isActive: boolean;
    sortOrder: number;
    metadata?: Record<string, any>;
    category?: any;
}
export declare const TemplateFieldSchema: import("mongoose").Schema<TemplateField, import("mongoose").Model<TemplateField, any, any, any, Document<unknown, any, TemplateField, any, {}> & TemplateField & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, TemplateField, Document<unknown, {}, import("mongoose").FlatRecord<TemplateField>, {}, import("mongoose").ResolveSchemaOptions<import("mongoose").DefaultSchemaOptions>> & import("mongoose").FlatRecord<TemplateField> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
export declare const TemplateSchema: import("mongoose").Schema<Template, import("mongoose").Model<Template, any, any, any, Document<unknown, any, Template, any, {}> & Template & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Template, Document<unknown, {}, import("mongoose").FlatRecord<Template>, {}, import("mongoose").ResolveSchemaOptions<import("mongoose").DefaultSchemaOptions>> & import("mongoose").FlatRecord<Template> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
