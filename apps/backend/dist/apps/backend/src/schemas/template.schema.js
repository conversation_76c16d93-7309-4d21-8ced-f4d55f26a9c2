"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateSchema = exports.TemplateFieldSchema = exports.Template = exports.TemplateField = exports.FieldType = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
var FieldType;
(function (FieldType) {
    FieldType["TEXT"] = "text";
    FieldType["TEXTAREA"] = "textarea";
    FieldType["NUMBER"] = "number";
    FieldType["SELECT"] = "select";
    FieldType["MULTISELECT"] = "multiselect";
    FieldType["CHECKBOX"] = "checkbox";
    FieldType["RADIO"] = "radio";
    FieldType["DATE"] = "date";
    FieldType["EMAIL"] = "email";
    FieldType["URL"] = "url";
    FieldType["FILE"] = "file";
    FieldType["IMAGE"] = "image";
})(FieldType || (exports.FieldType = FieldType = {}));
let TemplateField = class TemplateField {
};
exports.TemplateField = TemplateField;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], TemplateField.prototype, "id", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, trim: true }),
    __metadata("design:type", String)
], TemplateField.prototype, "name", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, trim: true }),
    __metadata("design:type", String)
], TemplateField.prototype, "label", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, enum: FieldType }),
    __metadata("design:type", String)
], TemplateField.prototype, "type", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], TemplateField.prototype, "required", void 0);
__decorate([
    (0, mongoose_1.Prop)({ trim: true }),
    __metadata("design:type", String)
], TemplateField.prototype, "placeholder", void 0);
__decorate([
    (0, mongoose_1.Prop)({ trim: true }),
    __metadata("design:type", String)
], TemplateField.prototype, "helpText", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [String], default: [] }),
    __metadata("design:type", Array)
], TemplateField.prototype, "options", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, default: {} }),
    __metadata("design:type", Object)
], TemplateField.prototype, "validation", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], TemplateField.prototype, "sortOrder", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], TemplateField.prototype, "isActive", void 0);
exports.TemplateField = TemplateField = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], TemplateField);
let Template = class Template {
};
exports.Template = Template;
__decorate([
    (0, mongoose_1.Prop)({ required: true, trim: true }),
    __metadata("design:type", String)
], Template.prototype, "name", void 0);
__decorate([
    (0, mongoose_1.Prop)({ trim: true }),
    __metadata("design:type", String)
], Template.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, ref: 'Category', required: true }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], Template.prototype, "categoryId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [TemplateField], default: [] }),
    __metadata("design:type", Array)
], Template.prototype, "fields", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], Template.prototype, "isActive", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], Template.prototype, "sortOrder", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, default: {} }),
    __metadata("design:type", Object)
], Template.prototype, "metadata", void 0);
exports.Template = Template = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true, collection: 'templates' })
], Template);
exports.TemplateFieldSchema = mongoose_1.SchemaFactory.createForClass(TemplateField);
exports.TemplateSchema = mongoose_1.SchemaFactory.createForClass(Template);
exports.TemplateSchema.index({ categoryId: 1 });
exports.TemplateSchema.index({ isActive: 1 });
exports.TemplateSchema.index({ sortOrder: 1 });
exports.TemplateSchema.virtual('category', {
    ref: 'Category',
    localField: 'categoryId',
    foreignField: '_id',
    justOne: true,
});
exports.TemplateSchema.set('toJSON', { virtuals: true });
exports.TemplateSchema.set('toObject', { virtuals: true });
//# sourceMappingURL=template.schema.js.map