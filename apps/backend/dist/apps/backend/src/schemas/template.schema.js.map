{"version": 3, "file": "template.schema.js", "sourceRoot": "", "sources": ["../../../../../src/schemas/template.schema.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+CAA+D;AAC/D,uCAA2C;AAI3C,IAAY,SAaX;AAbD,WAAY,SAAS;IACnB,0BAAa,CAAA;IACb,kCAAqB,CAAA;IACrB,8BAAiB,CAAA;IACjB,8BAAiB,CAAA;IACjB,wCAA2B,CAAA;IAC3B,kCAAqB,CAAA;IACrB,4BAAe,CAAA;IACf,0BAAa,CAAA;IACb,4BAAe,CAAA;IACf,wBAAW,CAAA;IACX,0BAAa,CAAA;IACb,4BAAe,CAAA;AACjB,CAAC,EAbW,SAAS,yBAAT,SAAS,QAapB;AAGM,IAAM,aAAa,GAAnB,MAAM,aAAa;CAiCzB,CAAA;AAjCY,sCAAa;AAExB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yCACd;AAGX;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;2CACxB;AAGb;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;4CACvB;AAGd;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;;2CAC1B;AAGhB;IADC,IAAA,eAAI,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;+CACP;AAGlB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;kDACA;AAGrB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;+CACH;AAGlB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;8CACpB;AAGlB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;iDACH;AAGjC;IADC,IAAA,eAAI,EAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;gDACH;AAGlB;IADC,IAAA,eAAI,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;+CACN;wBAhCP,aAAa;IADzB,IAAA,iBAAM,EAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;GACV,aAAa,CAiCzB;AAGM,IAAM,QAAQ,GAAd,MAAM,QAAQ;CAwBpB,CAAA;AAxBY,4BAAQ;AAEnB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;sCACxB;AAGb;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;6CACA;AAGrB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,gBAAK,CAAC,QAAQ,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BACpD,gBAAK,CAAC,QAAQ;4CAAC;AAG3B;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,aAAa,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;wCACrB;AAGxB;IADC,IAAA,eAAI,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;0CACN;AAGlB;IADC,IAAA,eAAI,EAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;2CACH;AAGlB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;0CACL;mBApBpB,QAAQ;IADpB,IAAA,iBAAM,EAAC,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC;GACzC,QAAQ,CAwBpB;AAEY,QAAA,mBAAmB,GAAG,wBAAa,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;AAClE,QAAA,cAAc,GAAG,wBAAa,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;AAGrE,sBAAc,CAAC,KAAK,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;AACxC,sBAAc,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AACtC,sBAAc,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;AAGvC,sBAAc,CAAC,OAAO,CAAC,UAAU,EAAE;IACjC,GAAG,EAAE,UAAU;IACf,UAAU,EAAE,YAAY;IACxB,YAAY,EAAE,KAAK;IACnB,OAAO,EAAE,IAAI;CACd,CAAC,CAAC;AAGH,sBAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AACjD,sBAAc,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC"}