"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ItemSchema = exports.ItemImageSchema = exports.Item = exports.ItemImage = exports.ItemCondition = exports.ItemStatus = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
var ItemStatus;
(function (ItemStatus) {
    ItemStatus["DRAFT"] = "draft";
    ItemStatus["ACTIVE"] = "active";
    ItemStatus["SOLD"] = "sold";
    ItemStatus["EXPIRED"] = "expired";
    ItemStatus["SUSPENDED"] = "suspended";
    ItemStatus["DELETED"] = "deleted";
})(ItemStatus || (exports.ItemStatus = ItemStatus = {}));
var ItemCondition;
(function (ItemCondition) {
    ItemCondition["NEW"] = "new";
    ItemCondition["LIKE_NEW"] = "like_new";
    ItemCondition["EXCELLENT"] = "excellent";
    ItemCondition["GOOD"] = "good";
    ItemCondition["FAIR"] = "fair";
    ItemCondition["POOR"] = "poor";
})(ItemCondition || (exports.ItemCondition = ItemCondition = {}));
let ItemImage = class ItemImage {
};
exports.ItemImage = ItemImage;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], ItemImage.prototype, "url", void 0);
__decorate([
    (0, mongoose_1.Prop)({ trim: true }),
    __metadata("design:type", String)
], ItemImage.prototype, "alt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], ItemImage.prototype, "sortOrder", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], ItemImage.prototype, "isPrimary", void 0);
exports.ItemImage = ItemImage = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], ItemImage);
let Item = class Item {
};
exports.Item = Item;
__decorate([
    (0, mongoose_1.Prop)({ required: true, trim: true }),
    __metadata("design:type", String)
], Item.prototype, "title", void 0);
__decorate([
    (0, mongoose_1.Prop)({ trim: true }),
    __metadata("design:type", String)
], Item.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, min: 0 }),
    __metadata("design:type", Number)
], Item.prototype, "price", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, default: 'USD' }),
    __metadata("design:type", String)
], Item.prototype, "currency", void 0);
__decorate([
    (0, mongoose_1.Prop)({ enum: ItemCondition }),
    __metadata("design:type", String)
], Item.prototype, "condition", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, ref: 'Category', required: true }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], Item.prototype, "categoryId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, ref: 'Template' }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], Item.prototype, "templateId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Item.prototype, "sellerId", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Item.prototype, "locationId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [ItemImage], default: [] }),
    __metadata("design:type", Array)
], Item.prototype, "images", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, default: {} }),
    __metadata("design:type", Object)
], Item.prototype, "customFields", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, enum: ItemStatus, default: ItemStatus.DRAFT }),
    __metadata("design:type", String)
], Item.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], Item.prototype, "views", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], Item.prototype, "favorites", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], Item.prototype, "isPromoted", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], Item.prototype, "isFeatured", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Date)
], Item.prototype, "expiresAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [String], default: [] }),
    __metadata("design:type", Array)
], Item.prototype, "tags", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, default: {} }),
    __metadata("design:type", Object)
], Item.prototype, "seoData", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, default: {} }),
    __metadata("design:type", Object)
], Item.prototype, "analytics", void 0);
exports.Item = Item = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true, collection: 'items' })
], Item);
exports.ItemImageSchema = mongoose_1.SchemaFactory.createForClass(ItemImage);
exports.ItemSchema = mongoose_1.SchemaFactory.createForClass(Item);
exports.ItemSchema.index({ sellerId: 1 });
exports.ItemSchema.index({ categoryId: 1 });
exports.ItemSchema.index({ status: 1 });
exports.ItemSchema.index({ price: 1 });
exports.ItemSchema.index({ createdAt: -1 });
exports.ItemSchema.index({ locationId: 1 });
exports.ItemSchema.index({ isPromoted: 1, isFeatured: 1 });
exports.ItemSchema.index({ expiresAt: 1 });
exports.ItemSchema.index({
    title: 'text',
    description: 'text',
    tags: 'text',
});
exports.ItemSchema.index({ status: 1, categoryId: 1, createdAt: -1 });
exports.ItemSchema.index({ sellerId: 1, status: 1, createdAt: -1 });
exports.ItemSchema.virtual('category', {
    ref: 'Category',
    localField: 'categoryId',
    foreignField: '_id',
    justOne: true,
});
exports.ItemSchema.virtual('template', {
    ref: 'Template',
    localField: 'templateId',
    foreignField: '_id',
    justOne: true,
});
exports.ItemSchema.set('toJSON', { virtuals: true });
exports.ItemSchema.set('toObject', { virtuals: true });
//# sourceMappingURL=item.schema.js.map