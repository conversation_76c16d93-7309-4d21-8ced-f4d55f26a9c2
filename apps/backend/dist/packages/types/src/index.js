"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationType = exports.SortOption = exports.ItemStatus = exports.ItemCondition = exports.FieldType = exports.UserRole = void 0;
var UserRole;
(function (UserRole) {
    UserRole["BUYER"] = "buyer";
    UserRole["SELLER"] = "seller";
    UserRole["ADMIN"] = "admin";
    UserRole["MODERATOR"] = "moderator";
})(UserRole || (exports.UserRole = UserRole = {}));
var FieldType;
(function (FieldType) {
    FieldType["TEXT"] = "text";
    FieldType["TEXTAREA"] = "textarea";
    FieldType["NUMBER"] = "number";
    FieldType["SELECT"] = "select";
    FieldType["MULTISELECT"] = "multiselect";
    FieldType["CHECKBOX"] = "checkbox";
    FieldType["RADIO"] = "radio";
    FieldType["DATE"] = "date";
    FieldType["EMAIL"] = "email";
    FieldType["URL"] = "url";
    FieldType["PHONE"] = "phone";
    FieldType["CURRENCY"] = "currency";
})(FieldType || (exports.FieldType = FieldType = {}));
var ItemCondition;
(function (ItemCondition) {
    ItemCondition["NEW"] = "new";
    ItemCondition["LIKE_NEW"] = "like_new";
    ItemCondition["GOOD"] = "good";
    ItemCondition["FAIR"] = "fair";
    ItemCondition["POOR"] = "poor";
})(ItemCondition || (exports.ItemCondition = ItemCondition = {}));
var ItemStatus;
(function (ItemStatus) {
    ItemStatus["DRAFT"] = "draft";
    ItemStatus["PENDING_APPROVAL"] = "pending_approval";
    ItemStatus["ACTIVE"] = "active";
    ItemStatus["SOLD"] = "sold";
    ItemStatus["EXPIRED"] = "expired";
    ItemStatus["REJECTED"] = "rejected";
    ItemStatus["REMOVED"] = "removed";
})(ItemStatus || (exports.ItemStatus = ItemStatus = {}));
var SortOption;
(function (SortOption) {
    SortOption["RELEVANCE"] = "relevance";
    SortOption["NEWEST"] = "newest";
    SortOption["OLDEST"] = "oldest";
    SortOption["PRICE_LOW_HIGH"] = "price_low_high";
    SortOption["PRICE_HIGH_LOW"] = "price_high_low";
    SortOption["DISTANCE"] = "distance";
})(SortOption || (exports.SortOption = SortOption = {}));
var NotificationType;
(function (NotificationType) {
    NotificationType["ITEM_APPROVED"] = "item_approved";
    NotificationType["ITEM_REJECTED"] = "item_rejected";
    NotificationType["ITEM_SOLD"] = "item_sold";
    NotificationType["MESSAGE_RECEIVED"] = "message_received";
    NotificationType["PRICE_DROP"] = "price_drop";
    NotificationType["SAVED_SEARCH_MATCH"] = "saved_search_match";
})(NotificationType || (exports.NotificationType = NotificationType = {}));
//# sourceMappingURL=index.js.map