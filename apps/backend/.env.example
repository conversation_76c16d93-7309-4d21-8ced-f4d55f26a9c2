# Application
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# Database - PostgreSQL (for users, auth, transactions)
DATABASE_URL="postgresql://username:password@localhost:5432/cansell?schema=public"

# Database - MongoDB (for items, categories, templates)
MONGODB_URL="mongodb://localhost:27017"
MONGODB_DB_NAME="cansell"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"
REFRESH_TOKEN_SECRET="your-super-secret-refresh-token-key-change-this-in-production"
REFRESH_TOKEN_EXPIRES_IN="30d"

# Redis (for caching and sessions)
REDIS_URL="redis://localhost:6379"

# File Storage
AWS_ACCESS_KEY_ID=""
AWS_SECRET_ACCESS_KEY=""
AWS_REGION="us-east-1"
AWS_S3_BUCKET=""

# Email Service
SMTP_HOST=""
SMTP_PORT=587
SMTP_USER=""
SMTP_PASS=""
FROM_EMAIL="<EMAIL>"

# External APIs
GOOGLE_MAPS_API_KEY=""
STRIPE_SECRET_KEY=""
STRIPE_WEBHOOK_SECRET=""

# Monitoring
SENTRY_DSN=""
