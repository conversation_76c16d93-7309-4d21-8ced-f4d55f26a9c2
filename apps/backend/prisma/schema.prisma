// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  ADMIN
  SELLER
  BUYER
}

enum TransactionStatus {
  PENDING
  COMPLETED
  CANCELLED
  REFUNDED
}

model User {
  id          String    @id @default(cuid())
  email       String    @unique
  username    String    @unique
  firstName   String
  lastName    String
  password    String
  role        UserRole  @default(BUYER)
  isVerified  Boolean   @default(false)
  isActive    Boolean   @default(true)
  avatar      String?
  phone       String?
  dateOfBirth DateTime?

  // Location
  locationId String?
  location   Location? @relation(fields: [locationId], references: [id])

  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  lastLoginAt DateTime?

  // Relations
  sellerProfile      SellerProfile?
  buyerProfile       BuyerProfile?
  buyerTransactions  Transaction[]  @relation("BuyerTransactions")
  sellerTransactions Transaction[]  @relation("SellerTransactions")
  givenReviews       Review[]       @relation("ReviewerReviews")
  receivedReviews    Review[]       @relation("ReceivedReviews")
  notifications      Notification[]

  @@map("users")
}

model SellerProfile {
  id     String @id @default(cuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  businessName String?
  businessType String?
  taxId        String?
  bankAccount  String?
  paypalEmail  String?

  // Ratings
  totalSales    Int   @default(0)
  averageRating Float @default(0)
  totalReviews  Int   @default(0)

  // Verification
  isVerified Boolean   @default(false)
  verifiedAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("seller_profiles")
}

model BuyerProfile {
  id     String @id @default(cuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Preferences
  preferredCategories String[]
  maxBudget           Float?
  currency            String   @default("USD")

  // Wishlist and favorites stored in MongoDB

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("buyer_profiles")
}

model Location {
  id       String     @id @default(cuid())
  name     String
  type     String // country, state, city
  code     String?
  parentId String?
  parent   Location?  @relation("LocationHierarchy", fields: [parentId], references: [id])
  children Location[] @relation("LocationHierarchy")

  // Coordinates
  latitude  Float?
  longitude Float?

  // Relations
  users User[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("locations")
}

model Transaction {
  id       String @id @default(cuid())
  buyerId  String
  sellerId String
  itemId   String // Reference to MongoDB item

  buyer  User @relation("BuyerTransactions", fields: [buyerId], references: [id])
  seller User @relation("SellerTransactions", fields: [sellerId], references: [id])

  amount   Float
  currency String            @default("USD")
  status   TransactionStatus @default(PENDING)

  // Payment details
  paymentMethod String?
  paymentId     String?

  // Shipping
  shippingAddress Json?
  trackingNumber  String?
  shippedAt       DateTime?
  deliveredAt     DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("transactions")
}

model Review {
  id            String  @id @default(cuid())
  reviewerId    String
  revieweeId    String
  transactionId String?
  itemId        String // Reference to MongoDB item

  reviewer User @relation("ReviewerReviews", fields: [reviewerId], references: [id])
  reviewee User @relation("ReceivedReviews", fields: [revieweeId], references: [id])

  rating  Int // 1-5 stars
  comment String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("reviews")
}

model Notification {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  type    String // email, push, in_app
  title   String
  message String
  data    Json? // Additional data

  isRead Boolean   @default(false)
  readAt DateTime?

  createdAt DateTime @default(now())

  @@map("notifications")
}

model VerificationCode {
  id    String @id @default(cuid())
  email String
  code  String
  type  String // email, sms, registration

  isUsed    Boolean   @default(false)
  usedAt    DateTime?
  expiresAt DateTime

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email, type])
  @@index([code])
  @@map("verification_codes")
}
