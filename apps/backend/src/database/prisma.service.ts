import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class PrismaService
  extends PrismaClient
  implements OnModuleInit, OnModuleDestroy
{
  async onModuleInit() {
    // Temporarily disable database connection for development
    // await this.$connect();
    console.log('⚠️  Database connection disabled for development');
  }

  async onModuleDestroy() {
    await this.$disconnect();
  }
}
