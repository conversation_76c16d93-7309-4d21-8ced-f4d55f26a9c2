import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as bcrypt from 'bcryptjs';
import { PrismaService } from './prisma.service';
import { Category, CategoryDocument } from '../schemas/category.schema';
import { Template, TemplateDocument } from '../schemas/template.schema';
import { UserRole } from '@prisma/client';

@Injectable()
export class SeedService {
  private readonly logger = new Logger(SeedService.name);

  constructor(
    private readonly prisma: PrismaService,
    @InjectModel(Category.name) private categoryModel: Model<CategoryDocument>,
    @InjectModel(Template.name) private templateModel: Model<TemplateDocument>,
  ) {}

  async seedAll() {
    this.logger.log('Starting database seeding...');

    try {
      await this.seedLocations();
      await this.seedUsers();
      await this.seedCategories();
      await this.seedTemplates();

      this.logger.log('Database seeding completed successfully!');
    } catch (error) {
      this.logger.error('Database seeding failed:', error);
      throw error;
    }
  }

  private async seedLocations() {
    this.logger.log('Seeding locations...');

    const existingLocations = await this.prisma.location.count();
    if (existingLocations > 0) {
      this.logger.log('Locations already exist, skipping...');
      return;
    }

    // Create countries
    const usa = await this.prisma.location.create({
      data: {
        name: 'United States',
        type: 'country',
        code: 'US',
      },
    });

    const canada = await this.prisma.location.create({
      data: {
        name: 'Canada',
        type: 'country',
        code: 'CA',
      },
    });

    // Create states for USA
    const california = await this.prisma.location.create({
      data: {
        name: 'California',
        type: 'state',
        code: 'CA',
        parentId: usa.id,
      },
    });

    const newYork = await this.prisma.location.create({
      data: {
        name: 'New York',
        type: 'state',
        code: 'NY',
        parentId: usa.id,
      },
    });

    // Create cities
    await this.prisma.location.createMany({
      data: [
        {
          name: 'Los Angeles',
          type: 'city',
          parentId: california.id,
          latitude: 34.0522,
          longitude: -118.2437,
        },
        {
          name: 'San Francisco',
          type: 'city',
          parentId: california.id,
          latitude: 37.7749,
          longitude: -122.4194,
        },
        {
          name: 'New York City',
          type: 'city',
          parentId: newYork.id,
          latitude: 40.7128,
          longitude: -74.0060,
        },
      ],
    });

    this.logger.log('Locations seeded successfully');
  }

  private async seedUsers() {
    this.logger.log('Seeding users...');

    const existingUsers = await this.prisma.user.count();
    if (existingUsers > 0) {
      this.logger.log('Users already exist, skipping...');
      return;
    }

    const hashedPassword = await bcrypt.hash('admin123!', 12);

    // Create admin user
    const adminUser = await this.prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'admin',
        firstName: 'Admin',
        lastName: 'User',
        password: hashedPassword,
        role: UserRole.ADMIN,
        isVerified: true,
        isActive: true,
      },
    });

    // Create test seller
    const sellerPassword = await bcrypt.hash('seller123!', 12);
    const sellerUser = await this.prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'testseller',
        firstName: 'Test',
        lastName: 'Seller',
        password: sellerPassword,
        role: UserRole.SELLER,
        isVerified: true,
        isActive: true,
      },
    });

    // Create seller profile
    await this.prisma.sellerProfile.create({
      data: {
        userId: sellerUser.id,
        businessName: 'Test Electronics Store',
        businessType: 'Electronics',
        isVerified: true,
        verifiedAt: new Date(),
      },
    });

    // Create test buyer
    const buyerPassword = await bcrypt.hash('buyer123!', 12);
    const buyerUser = await this.prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'testbuyer',
        firstName: 'Test',
        lastName: 'Buyer',
        password: buyerPassword,
        role: UserRole.BUYER,
        isVerified: true,
        isActive: true,
      },
    });

    // Create buyer profile
    await this.prisma.buyerProfile.create({
      data: {
        userId: buyerUser.id,
        preferredCategories: ['electronics', 'smartphones'],
        maxBudget: 2000,
        currency: 'USD',
      },
    });

    this.logger.log('Users seeded successfully');
  }

  private async seedCategories() {
    this.logger.log('Seeding categories...');

    const existingCategories = await this.categoryModel.countDocuments();
    if (existingCategories > 0) {
      this.logger.log('Categories already exist, skipping...');
      return;
    }

    // Create root categories
    const electronics = await this.categoryModel.create({
      name: 'Electronics',
      slug: 'electronics',
      description: 'Electronic devices and gadgets',
      isActive: true,
      sortOrder: 1,
      icon: 'smartphone',
    });

    const vehicles = await this.categoryModel.create({
      name: 'Vehicles',
      slug: 'vehicles',
      description: 'Cars, motorcycles, and other vehicles',
      isActive: true,
      sortOrder: 2,
      icon: 'car',
    });

    const fashion = await this.categoryModel.create({
      name: 'Fashion',
      slug: 'fashion',
      description: 'Clothing, shoes, and accessories',
      isActive: true,
      sortOrder: 3,
      icon: 'shirt',
    });

    // Create subcategories for Electronics
    await this.categoryModel.create({
      name: 'Smartphones',
      slug: 'smartphones',
      description: 'Mobile phones and accessories',
      parentId: electronics._id,
      isActive: true,
      sortOrder: 1,
    });

    await this.categoryModel.create({
      name: 'Laptops',
      slug: 'laptops',
      description: 'Laptops and notebooks',
      parentId: electronics._id,
      isActive: true,
      sortOrder: 2,
    });

    await this.categoryModel.create({
      name: 'Gaming',
      slug: 'gaming',
      description: 'Gaming consoles and accessories',
      parentId: electronics._id,
      isActive: true,
      sortOrder: 3,
    });

    this.logger.log('Categories seeded successfully');
  }

  private async seedTemplates() {
    this.logger.log('Seeding templates...');

    const existingTemplates = await this.templateModel.countDocuments();
    if (existingTemplates > 0) {
      this.logger.log('Templates already exist, skipping...');
      return;
    }

    // Find categories
    const smartphonesCategory = await this.categoryModel.findOne({ slug: 'smartphones' });
    const laptopsCategory = await this.categoryModel.findOne({ slug: 'laptops' });

    if (smartphonesCategory) {
      await this.templateModel.create({
        name: 'Smartphone Template',
        description: 'Template for listing smartphones',
        categoryId: smartphonesCategory._id,
        fields: [
          {
            id: 'brand',
            name: 'brand',
            label: 'Brand',
            type: 'select',
            required: true,
            options: ['Apple', 'Samsung', 'Google', 'OnePlus', 'Xiaomi', 'Other'],
            sortOrder: 1,
          },
          {
            id: 'model',
            name: 'model',
            label: 'Model',
            type: 'text',
            required: true,
            placeholder: 'e.g., iPhone 15 Pro',
            sortOrder: 2,
          },
          {
            id: 'storage',
            name: 'storage',
            label: 'Storage Capacity',
            type: 'select',
            required: true,
            options: ['64GB', '128GB', '256GB', '512GB', '1TB'],
            sortOrder: 3,
          },
          {
            id: 'color',
            name: 'color',
            label: 'Color',
            type: 'text',
            required: false,
            placeholder: 'e.g., Space Gray',
            sortOrder: 4,
          },
        ],
        isActive: true,
        sortOrder: 1,
      });
    }

    if (laptopsCategory) {
      await this.templateModel.create({
        name: 'Laptop Template',
        description: 'Template for listing laptops',
        categoryId: laptopsCategory._id,
        fields: [
          {
            id: 'brand',
            name: 'brand',
            label: 'Brand',
            type: 'select',
            required: true,
            options: ['Apple', 'Dell', 'HP', 'Lenovo', 'ASUS', 'Acer', 'Other'],
            sortOrder: 1,
          },
          {
            id: 'processor',
            name: 'processor',
            label: 'Processor',
            type: 'text',
            required: true,
            placeholder: 'e.g., Intel Core i7',
            sortOrder: 2,
          },
          {
            id: 'ram',
            name: 'ram',
            label: 'RAM',
            type: 'select',
            required: true,
            options: ['4GB', '8GB', '16GB', '32GB', '64GB'],
            sortOrder: 3,
          },
          {
            id: 'storage',
            name: 'storage',
            label: 'Storage',
            type: 'text',
            required: true,
            placeholder: 'e.g., 512GB SSD',
            sortOrder: 4,
          },
        ],
        isActive: true,
        sortOrder: 1,
      });
    }

    this.logger.log('Templates seeded successfully');
  }
}
