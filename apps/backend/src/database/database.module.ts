import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { PrismaService } from './prisma.service';
import { SeedService } from './seed.service';
import { Category, CategorySchema } from '../schemas/category.schema';
import { Template, TemplateSchema } from '../schemas/template.schema';

@Global()
@Module({
  imports: [
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('MONGODB_URL'),
        dbName: configService.get<string>('MONGODB_DB_NAME', 'cansell'),
      }),
      inject: [ConfigService],
    }),
    MongooseModule.forFeature([
      { name: Category.name, schema: CategorySchema },
      { name: Template.name, schema: TemplateSchema },
    ]),
  ],
  providers: [PrismaService, SeedService],
  exports: [PrismaService, SeedService, MongooseModule],
})
export class DatabaseModule {}
