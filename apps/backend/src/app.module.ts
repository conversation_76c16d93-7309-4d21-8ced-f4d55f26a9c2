import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { MongooseModule } from '@nestjs/mongoose';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './database/database.module';
import { AuthModule } from './modules/auth/auth.module';
// import { CatalogModule } from './modules/catalog/catalog.module';
import { ItemModule } from './modules/item/item.module';
import { LocationModule } from './modules/location/location.module';
import { SearchModule } from './modules/search/search.module';
import { NotificationModule } from './modules/notification/notification.module';
// import { MediaModule } from './modules/media/media.module';
import { Category, CategorySchema } from './schemas/category.schema';
import { Template, TemplateSchema } from './schemas/template.schema';
import { Item, ItemSchema } from './schemas/item.schema';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    ThrottlerModule.forRoot([
      {
        ttl: 60000,
        limit: 100,
      },
    ]),
    DatabaseModule,
    MongooseModule.forFeature([
      { name: Category.name, schema: CategorySchema },
      { name: Template.name, schema: TemplateSchema },
      { name: Item.name, schema: ItemSchema },
    ]),
    AuthModule,
    // CatalogModule, // Temporarily disabled due to compilation errors
    ItemModule,
    LocationModule,
    SearchModule,
    NotificationModule,
    // MediaModule, // Temporarily disabled due to compilation errors
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
