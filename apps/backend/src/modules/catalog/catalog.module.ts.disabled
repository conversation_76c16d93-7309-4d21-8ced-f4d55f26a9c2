import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
// import { CatalogController } from './catalog.controller';
// import { CatalogService } from './catalog.service';
import { Category, CategorySchema } from '../../schemas/category.schema';
import { Template, TemplateSchema } from '../../schemas/template.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Category.name, schema: CategorySchema },
      { name: Template.name, schema: TemplateSchema },
    ]),
  ],
  controllers: [
    /* CatalogController */
  ],
  providers: [
    /* CatalogService */
  ],
  exports: [
    /* CatalogService */
  ],
})
export class CatalogModule {}
