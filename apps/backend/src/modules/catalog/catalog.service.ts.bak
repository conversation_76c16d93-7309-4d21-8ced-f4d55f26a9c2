import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Category, CategoryDocument } from '../../schemas/category.schema';
import { Template, TemplateDocument } from '../../schemas/template.schema';

@Injectable()
export class CatalogService {
  constructor(
    @InjectModel(Category.name) private categoryModel: Model<CategoryDocument>,
    @InjectModel(Template.name) private templateModel: Model<TemplateDocument>,
  ) {}

  async getCategories() {
    return await this.categoryModel
      .find({ isActive: true })
      .populate('subcategories')
      .sort({ sortOrder: 1, name: 1 })
      .exec();
  }

  async getCategoryById(id: string) {
    const category = await this.categoryModel
      .findById(id)
      .populate('subcategories')
      .populate('templates')
      .exec();

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    return category;
  }

  async getCategoryBySlug(slug: string) {
    const category = await this.categoryModel
      .findOne({ slug, isActive: true })
      .populate('subcategories')
      .populate('templates')
      .exec();

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    return category;
  }

  async createCategory(categoryData: any) {
    const category = new this.categoryModel(categoryData);
    return await category.save();
  }

  async updateCategory(id: string, updateData: any) {
    const category = await this.categoryModel
      .findByIdAndUpdate(id, updateData, { new: true })
      .exec();

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    return category;
  }

  async deleteCategory(id: string) {
    const category = await this.categoryModel.findByIdAndDelete(id).exec();

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    return { message: 'Category deleted successfully' };
  }

  async getTemplates() {
    return await this.templateModel
      .find({ isActive: true })
      .populate('category')
      .sort({ sortOrder: 1, name: 1 })
      .exec();
  }

  async getTemplateById(id: string) {
    const template = await this.templateModel
      .findById(id)
      .populate('category')
      .exec();

    if (!template) {
      throw new NotFoundException('Template not found');
    }

    return template;
  }

  async getTemplatesByCategory(categoryId: string) {
    return await this.templateModel
      .find({ categoryId, isActive: true })
      .sort({ sortOrder: 1, name: 1 })
      .exec();
  }

  async createTemplate(templateData: any) {
    const template = new this.templateModel(templateData);
    return await template.save();
  }

  async updateTemplate(id: string, updateData: any) {
    const template = await this.templateModel
      .findByIdAndUpdate(id, updateData, { new: true })
      .exec();

    if (!template) {
      throw new NotFoundException('Template not found');
    }

    return template;
  }

  async deleteTemplate(id: string) {
    const template = await this.templateModel.findByIdAndDelete(id).exec();

    if (!template) {
      throw new NotFoundException('Template not found');
    }

    return { message: 'Template deleted successfully' };
  }

  // Legacy mock data methods - keeping for backward compatibility
  private readonly mockCategories: any[] = [
    {
      id: '1',
      name: 'Electronics',
      slug: 'electronics',
      description: 'Electronic devices and gadgets',
      parentId: null,
      templateIds: ['1', '2'],
      isActive: true,
      sortOrder: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '2',
      name: 'Smartphones',
      slug: 'smartphones',
      description: 'Mobile phones and accessories',
      parentId: '1',
      templateIds: ['1'],
      isActive: true,
      sortOrder: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '3',
      name: 'Vehicles',
      slug: 'vehicles',
      description: 'Cars, motorcycles, and other vehicles',
      parentId: null,
      templateIds: ['3'],
      isActive: true,
      sortOrder: 2,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  private readonly mockTemplates: Template[] = [
    {
      id: '1',
      name: 'Smartphone Template',
      description: 'Template for listing smartphones',
      categoryId: '2',
      fields: [
        {
          id: '1',
          name: 'brand',
          label: 'Brand',
          type: FieldType.SELECT,
          required: true,
          options: ['Apple', 'Samsung', 'Google', 'OnePlus', 'Other'],
          sortOrder: 1,
        },
        {
          id: '2',
          name: 'model',
          label: 'Model',
          type: FieldType.TEXT,
          required: true,
          placeholder: 'e.g., iPhone 15 Pro',
          sortOrder: 2,
        },
        {
          id: '3',
          name: 'storage',
          label: 'Storage Capacity',
          type: FieldType.SELECT,
          required: true,
          options: ['64GB', '128GB', '256GB', '512GB', '1TB'],
          sortOrder: 3,
        },
        {
          id: '4',
          name: 'color',
          label: 'Color',
          type: FieldType.TEXT,
          required: false,
          placeholder: 'e.g., Space Gray',
          sortOrder: 4,
        },
      ],
      isActive: true,
      version: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  async getCategories(parentId?: string): Promise<Category[]> {
    if (parentId) {
      return this.mockCategories.filter((cat) => cat.parentId === parentId);
    }
    return this.mockCategories.filter((cat) => cat.parentId === null);
  }

  async getCategoryById(id: string): Promise<Category> {
    const category = this.mockCategories.find((cat) => cat.id === id);
    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }
    return category;
  }

  async getTemplatesByCategory(categoryId: string): Promise<Template[]> {
    const category = await this.getCategoryById(categoryId);
    return this.mockTemplates.filter((template) =>
      category.templateIds.includes(template.id),
    );
  }

  async getTemplateById(id: string): Promise<Template> {
    const template = this.mockTemplates.find((t) => t.id === id);
    if (!template) {
      throw new NotFoundException(`Template with ID ${id} not found`);
    }
    return template;
  }
}
