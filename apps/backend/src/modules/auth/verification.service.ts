import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../database/prisma.service';
import { GenerateVerificationCodeDto, VerifyCodeDto } from './dto/verify-code.dto';

@Injectable()
export class VerificationService {
  constructor(private readonly prisma: PrismaService) {}

  async generateVerificationCode(dto: GenerateVerificationCodeDto) {
    // Generate a 6-digit random code
    const code = Math.floor(100000 + Math.random() * 900000).toString();
    
    // Set expiration time (15 minutes from now)
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 15);

    // Invalidate any existing codes for this email and type
    await this.prisma.verificationCode.updateMany({
      where: {
        email: dto.email,
        type: dto.type,
        isUsed: false,
      },
      data: {
        isUsed: true,
        usedAt: new Date(),
      },
    });

    // Create new verification code
    const verificationCode = await this.prisma.verificationCode.create({
      data: {
        email: dto.email,
        code,
        type: dto.type,
        expiresAt,
      },
    });

    // In a real application, you would send the code via email or SMS here
    // For now, we'll return the code for development purposes
    return {
      message: 'Verification code generated successfully',
      // TODO: Remove this in production - only for development
      code: code,
      expiresAt,
    };
  }

  async verifyCode(dto: VerifyCodeDto): Promise<boolean> {
    const verificationCode = await this.prisma.verificationCode.findFirst({
      where: {
        email: dto.email,
        code: dto.code,
        type: dto.type,
        isUsed: false,
      },
    });

    if (!verificationCode) {
      throw new BadRequestException('Invalid or expired verification code');
    }

    // Check if code has expired
    if (new Date() > verificationCode.expiresAt) {
      throw new BadRequestException('Verification code has expired');
    }

    // Mark code as used
    await this.prisma.verificationCode.update({
      where: { id: verificationCode.id },
      data: {
        isUsed: true,
        usedAt: new Date(),
      },
    });

    return true;
  }

  async isCodeValid(email: string, code: string, type: string): Promise<boolean> {
    const verificationCode = await this.prisma.verificationCode.findFirst({
      where: {
        email,
        code,
        type,
        isUsed: false,
      },
    });

    if (!verificationCode) {
      return false;
    }

    // Check if code has expired
    if (new Date() > verificationCode.expiresAt) {
      return false;
    }

    return true;
  }

  async cleanupExpiredCodes() {
    // Clean up expired codes (older than 24 hours)
    const oneDayAgo = new Date();
    oneDayAgo.setHours(oneDayAgo.getHours() - 24);

    await this.prisma.verificationCode.deleteMany({
      where: {
        OR: [
          { expiresAt: { lt: new Date() } },
          { createdAt: { lt: oneDayAgo } },
        ],
      },
    });
  }
}
