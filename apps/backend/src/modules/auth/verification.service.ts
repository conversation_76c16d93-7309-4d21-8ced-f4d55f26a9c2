import {
  Injectable,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
// import { PrismaService } from '../../database/prisma.service';
import {
  GenerateVerificationCodeDto,
  VerifyCodeDto,
} from './dto/verify-code.dto';

@Injectable()
export class VerificationService {
  // Temporary in-memory storage for development
  private codes = new Map<
    string,
    { code: string; expiresAt: Date; isUsed: boolean }
  >();

  constructor() {} // private readonly prisma: PrismaService

  async generateVerificationCode(dto: GenerateVerificationCodeDto) {
    // Generate a 6-digit random code
    const code = Math.floor(100000 + Math.random() * 900000).toString();

    // Set expiration time (15 minutes from now)
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 15);

    // Store in memory for development
    const key = `${dto.email}:${dto.type}`;
    this.codes.set(key, {
      code,
      expiresAt,
      isUsed: false,
    });

    // In a real application, you would send the code via email or SMS here
    // For now, we'll return the code for development purposes
    return {
      message: 'Verification code generated successfully',
      // TODO: Remove this in production - only for development
      code: code,
      expiresAt,
    };
  }

  async verifyCode(dto: VerifyCodeDto): Promise<boolean> {
    const key = `${dto.email}:${dto.type}`;
    const verificationCode = this.codes.get(key);

    if (!verificationCode) {
      throw new BadRequestException('Invalid or expired verification code');
    }

    if (verificationCode.code !== dto.code) {
      throw new BadRequestException('Invalid verification code');
    }

    if (verificationCode.isUsed) {
      throw new BadRequestException('Verification code has already been used');
    }

    // Check if code has expired
    if (new Date() > verificationCode.expiresAt) {
      throw new BadRequestException('Verification code has expired');
    }

    // Mark code as used
    verificationCode.isUsed = true;

    return true;
  }

  async isCodeValid(
    email: string,
    code: string,
    type: string,
  ): Promise<boolean> {
    const key = `${email}:${type}`;
    const verificationCode = this.codes.get(key);

    if (!verificationCode) {
      return false;
    }

    if (verificationCode.code !== code) {
      return false;
    }

    if (verificationCode.isUsed) {
      return false;
    }

    // Check if code has expired
    if (new Date() > verificationCode.expiresAt) {
      return false;
    }

    return true;
  }

  async cleanupExpiredCodes() {
    // Clean up expired codes from memory
    const now = new Date();
    for (const [key, value] of this.codes.entries()) {
      if (now > value.expiresAt) {
        this.codes.delete(key);
      }
    }
  }
}
