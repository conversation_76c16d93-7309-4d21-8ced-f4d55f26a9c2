import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
import { PrismaService } from '../../database/prisma.service';
import { RegisterDto } from './dto/register.dto';
import { UserRole } from '@prisma/client';
import { VerificationService } from './verification.service';

@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly verificationService: VerificationService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.findUserByEmail(email);
    if (user && (await bcrypt.compare(password, user.password))) {
      const { password: _, ...result } = user;
      return result;
    }
    return null;
  }

  async register(registerDto: RegisterDto) {
    const existingUser = await this.findUserByEmail(registerDto.email);
    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Verify the verification code
    const isCodeValid = await this.verificationService.isCodeValid(
      registerDto.email,
      registerDto.verificationCode,
      'registration',
    );

    if (!isCodeValid) {
      throw new BadRequestException('Invalid or expired verification code');
    }

    // Mark the verification code as used
    await this.verificationService.verifyCode({
      email: registerDto.email,
      code: registerDto.verificationCode,
      type: 'registration',
    });

    const hashedPassword = await bcrypt.hash(registerDto.password, 12);

    const user = await this.createUser({
      ...registerDto,
      password: hashedPassword,
    });

    const { password: _, ...result } = user;
    const tokens = await this.generateTokens(result);

    return {
      user: result,
      ...tokens,
    };
  }

  async login(user: any) {
    await this.updateLastLogin(user.id);
    const tokens = await this.generateTokens(user);
    return {
      user,
      ...tokens,
    };
  }

  async refreshToken(user: any) {
    const tokens = await this.generateTokens(user);
    return tokens;
  }

  async logout(userId: string) {
    // TODO: Implement token blacklisting
    return { message: 'Successfully logged out' };
  }

  private async generateTokens(user: any) {
    const payload = { email: user.email, sub: user.id, role: user.role };

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload),
      this.jwtService.signAsync(payload, {
        secret: this.configService.get('REFRESH_TOKEN_SECRET'),
        expiresIn: this.configService.get('REFRESH_TOKEN_EXPIRES_IN', '30d'),
      }),
    ]);

    return {
      accessToken,
      refreshToken,
      expiresIn: this.configService.get('JWT_EXPIRES_IN', '7d'),
    };
  }

  private async findUserByEmail(email: string) {
    return await this.prisma.user.findUnique({
      where: { email },
      include: {
        sellerProfile: true,
        buyerProfile: true,
        location: true,
      },
    });
  }

  private async createUser(userData: any) {
    const user = await this.prisma.user.create({
      data: {
        email: userData.email,
        username: userData.username || userData.email.split('@')[0],
        firstName: userData.firstName,
        lastName: userData.lastName,
        password: userData.password,
        role: userData.role || UserRole.BUYER,
        isVerified: false,
      },
      include: {
        sellerProfile: true,
        buyerProfile: true,
        location: true,
      },
    });

    // Create profile based on role
    if (user.role === UserRole.SELLER) {
      await this.prisma.sellerProfile.create({
        data: {
          userId: user.id,
        },
      });
    } else if (user.role === UserRole.BUYER) {
      await this.prisma.buyerProfile.create({
        data: {
          userId: user.id,
        },
      });
    }

    return user;
  }

  async updateLastLogin(userId: string) {
    await this.prisma.user.update({
      where: { id: userId },
      data: { lastLoginAt: new Date() },
    });
  }
}
