import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
// import { PrismaService } from '../../database/prisma.service';
import { RegisterDto } from './dto/register.dto';
// import { UserRole } from '@prisma/client';
import { VerificationService } from './verification.service';

@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    // private readonly prisma: PrismaService,
    private readonly verificationService: VerificationService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    // Mock user validation for development
    if (email === '<EMAIL>' && password === 'password123') {
      return {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        role: 'BUYER',
        isVerified: true,
      };
    }
    return null;
  }

  async register(registerDto: RegisterDto) {
    // Mock check for existing user
    if (registerDto.email === '<EMAIL>') {
      throw new ConflictException('User with this email already exists');
    }

    // Verify the verification code
    const isCodeValid = await this.verificationService.isCodeValid(
      registerDto.email,
      registerDto.verificationCode,
      'registration',
    );

    if (!isCodeValid) {
      throw new BadRequestException('Invalid or expired verification code');
    }

    // Mark the verification code as used
    await this.verificationService.verifyCode({
      email: registerDto.email,
      code: registerDto.verificationCode,
      type: 'registration',
    });

    // Mock user creation for development
    const user = {
      id: Math.random().toString(36).substr(2, 9),
      email: registerDto.email,
      username: registerDto.email.split('@')[0],
      firstName: registerDto.firstName,
      lastName: registerDto.lastName,
      role: 'BUYER',
      isVerified: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const tokens = await this.generateTokens(user);

    return {
      user,
      ...tokens,
    };
  }

  async login(user: any) {
    // await this.updateLastLogin(user.id); // Commented out for development
    const tokens = await this.generateTokens(user);
    return {
      user,
      ...tokens,
    };
  }

  async refreshToken(user: any) {
    const tokens = await this.generateTokens(user);
    return tokens;
  }

  async logout(userId: string) {
    // TODO: Implement token blacklisting
    return { message: 'Successfully logged out' };
  }

  private async generateTokens(user: any) {
    const payload = { email: user.email, sub: user.id, role: user.role };

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload),
      this.jwtService.signAsync(payload, {
        secret: this.configService.get('REFRESH_TOKEN_SECRET'),
        expiresIn: this.configService.get('REFRESH_TOKEN_EXPIRES_IN', '30d'),
      }),
    ]);

    return {
      accessToken,
      refreshToken,
      expiresIn: this.configService.get('JWT_EXPIRES_IN', '7d'),
    };
  }

  // Commented out for development - no database connection
  // private async findUserByEmail(email: string) {
  //   return await this.prisma.user.findUnique({
  //     where: { email },
  //     include: {
  //       sellerProfile: true,
  //       buyerProfile: true,
  //       location: true,
  //     },
  //   });
  // }

  // private async createUser(userData: any) {
  //   const user = await this.prisma.user.create({
  //     data: {
  //       email: userData.email,
  //       username: userData.username || userData.email.split('@')[0],
  //       firstName: userData.firstName,
  //       lastName: userData.lastName,
  //       password: userData.password,
  //       role: userData.role || UserRole.BUYER,
  //       isVerified: false,
  //     },
  //     include: {
  //       sellerProfile: true,
  //       buyerProfile: true,
  //       location: true,
  //     },
  //   });

  //   // Create profile based on role
  //   if (user.role === UserRole.SELLER) {
  //     await this.prisma.sellerProfile.create({
  //       data: {
  //         userId: user.id,
  //       },
  //     });
  //   } else if (user.role === UserRole.BUYER) {
  //     await this.prisma.buyerProfile.create({
  //       data: {
  //         userId: user.id,
  //       },
  //     });
  //   }

  //   return user;
  // }

  // async updateLastLogin(userId: string) {
  //   await this.prisma.user.update({
  //     where: { id: userId },
  //     data: { lastLoginAt: new Date() },
  //   });
  // }
}
