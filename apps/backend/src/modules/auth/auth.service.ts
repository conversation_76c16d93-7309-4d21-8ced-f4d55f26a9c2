import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
// import { PrismaService } from '../../database/prisma.service';
import { RegisterDto } from './dto/register.dto';
// import { UserRole } from '@prisma/client';
import { VerificationService } from './verification.service';

// In-memory user storage for development (replace with database in production)
interface User {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  password: string;
  role: 'BUYER' | 'SELLER' | 'ADMIN';
  isVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
}

// In-memory token blacklist for development
const tokenBlacklist = new Set<string>();

@Injectable()
export class AuthService {
  private users: Map<string, User> = new Map();

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    // private readonly prisma: PrismaService,
    private readonly verificationService: VerificationService,
  ) {
    // Initialize with a test user for development
    this.initializeTestUsers();
  }

  private async initializeTestUsers() {
    // Create test users with hashed passwords
    const testUsers = [
      {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        password: 'password123',
        role: 'BUYER' as const,
        isVerified: true,
      },
      {
        id: '2',
        email: '<EMAIL>',
        username: 'admin',
        firstName: 'Admin',
        lastName: 'User',
        password: 'admin123',
        role: 'ADMIN' as const,
        isVerified: true,
      },
    ];

    for (const userData of testUsers) {
      const hashedPassword = await bcrypt.hash(userData.password, 12);
      const user: User = {
        ...userData,
        password: hashedPassword,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      this.users.set(user.email, user);
    }
  }

  async validateUser(email: string, password: string): Promise<any> {
    const user = this.users.get(email);
    if (!user) {
      return null;
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return null;
    }

    // Return user without password
    const { password: _, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  async register(registerDto: RegisterDto) {
    // Check if user already exists
    if (this.users.has(registerDto.email)) {
      throw new ConflictException('User with this email already exists');
    }

    // Verify the verification code
    const isCodeValid = await this.verificationService.isCodeValid(
      registerDto.email,
      registerDto.verificationCode,
      'registration',
    );

    if (!isCodeValid) {
      throw new BadRequestException('Invalid or expired verification code');
    }

    // Mark the verification code as used
    await this.verificationService.verifyCode({
      email: registerDto.email,
      code: registerDto.verificationCode,
      type: 'registration',
    });

    // Hash the password
    const hashedPassword = await bcrypt.hash(registerDto.password, 12);

    // Create new user
    const user: User = {
      id: Math.random().toString(36).substr(2, 9),
      email: registerDto.email,
      username: registerDto.email.split('@')[0],
      firstName: registerDto.firstName,
      lastName: registerDto.lastName,
      password: hashedPassword,
      role: 'BUYER',
      isVerified: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Store user
    this.users.set(user.email, user);

    // Generate tokens
    const { password: _, ...userWithoutPassword } = user;
    const tokens = await this.generateTokens(userWithoutPassword);

    return {
      user: userWithoutPassword,
      ...tokens,
    };
  }

  async login(user: any) {
    // Update last login time
    const storedUser = this.users.get(user.email);
    if (storedUser) {
      storedUser.lastLoginAt = new Date();
      storedUser.updatedAt = new Date();
      this.users.set(user.email, storedUser);
    }

    const tokens = await this.generateTokens(user);
    return {
      user,
      ...tokens,
    };
  }

  async refreshToken(refreshToken: string) {
    try {
      // Verify refresh token
      const payload = await this.jwtService.verifyAsync(refreshToken, {
        secret: this.configService.get('REFRESH_TOKEN_SECRET'),
      });

      // Check if token is blacklisted
      if (tokenBlacklist.has(refreshToken)) {
        throw new UnauthorizedException('Token has been revoked');
      }

      // Get user
      const user = this.users.get(payload.email);
      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      // Generate new tokens
      const { password: _, ...userWithoutPassword } = user;
      const tokens = await this.generateTokens(userWithoutPassword);

      // Blacklist the old refresh token
      tokenBlacklist.add(refreshToken);

      return tokens;
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async logout(refreshToken?: string) {
    if (refreshToken) {
      // Add refresh token to blacklist
      tokenBlacklist.add(refreshToken);
    }
    return { message: 'Successfully logged out' };
  }

  async validateToken(token: string): Promise<any> {
    try {
      const payload = await this.jwtService.verifyAsync(token);
      const user = this.users.get(payload.email);
      if (!user) {
        throw new UnauthorizedException('User not found');
      }
      const { password: _, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }

  private async generateTokens(user: any) {
    const payload = { email: user.email, sub: user.id, role: user.role };

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload),
      this.jwtService.signAsync(payload, {
        secret: this.configService.get('REFRESH_TOKEN_SECRET'),
        expiresIn: this.configService.get('REFRESH_TOKEN_EXPIRES_IN', '30d'),
      }),
    ]);

    return {
      accessToken,
      refreshToken,
      expiresIn: this.configService.get('JWT_EXPIRES_IN', '7d'),
    };
  }

  // Commented out for development - no database connection
  // private async findUserByEmail(email: string) {
  //   return await this.prisma.user.findUnique({
  //     where: { email },
  //     include: {
  //       sellerProfile: true,
  //       buyerProfile: true,
  //       location: true,
  //     },
  //   });
  // }

  // private async createUser(userData: any) {
  //   const user = await this.prisma.user.create({
  //     data: {
  //       email: userData.email,
  //       username: userData.username || userData.email.split('@')[0],
  //       firstName: userData.firstName,
  //       lastName: userData.lastName,
  //       password: userData.password,
  //       role: userData.role || UserRole.BUYER,
  //       isVerified: false,
  //     },
  //     include: {
  //       sellerProfile: true,
  //       buyerProfile: true,
  //       location: true,
  //     },
  //   });

  //   // Create profile based on role
  //   if (user.role === UserRole.SELLER) {
  //     await this.prisma.sellerProfile.create({
  //       data: {
  //         userId: user.id,
  //       },
  //     });
  //   } else if (user.role === UserRole.BUYER) {
  //     await this.prisma.buyerProfile.create({
  //       data: {
  //         userId: user.id,
  //       },
  //     });
  //   }

  //   return user;
  // }

  // async updateLastLogin(userId: string) {
  //   await this.prisma.user.update({
  //     where: { id: userId },
  //     data: { lastLoginAt: new Date() },
  //   });
  // }
}
