import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ItemDocument = Item & Document;

export enum ItemStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  SOLD = 'sold',
  EXPIRED = 'expired',
  SUSPENDED = 'suspended',
  DELETED = 'deleted',
}

export enum ItemCondition {
  NEW = 'new',
  LIKE_NEW = 'like_new',
  EXCELLENT = 'excellent',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor',
}

@Schema({ _id: false })
export class ItemImage {
  @Prop({ required: true })
  url: string;

  @Prop({ trim: true })
  alt?: string;

  @Prop({ default: 0 })
  sortOrder: number;

  @Prop({ default: false })
  isPrimary: boolean;
}

@Schema({ timestamps: true, collection: 'items' })
export class Item {
  @Prop({ required: true, trim: true })
  title: string;

  @Prop({ trim: true })
  description?: string;

  @Prop({ required: true, min: 0 })
  price: number;

  @Prop({ required: true, default: 'USD' })
  currency: string;

  @Prop({ enum: ItemCondition })
  condition?: ItemCondition;

  @Prop({ type: Types.ObjectId, ref: 'Category', required: true })
  categoryId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Template' })
  templateId?: Types.ObjectId;

  @Prop({ required: true })
  sellerId: string; // Reference to PostgreSQL User

  @Prop()
  locationId?: string; // Reference to PostgreSQL Location

  @Prop({ type: [ItemImage], default: [] })
  images: ItemImage[];

  @Prop({ type: Object, default: {} })
  customFields?: Record<string, any>;

  @Prop({ required: true, enum: ItemStatus, default: ItemStatus.DRAFT })
  status: ItemStatus;

  @Prop({ default: 0 })
  views: number;

  @Prop({ default: 0 })
  favorites: number;

  @Prop({ default: false })
  isPromoted: boolean;

  @Prop({ default: false })
  isFeatured: boolean;

  @Prop()
  expiresAt?: Date;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ type: Object, default: {} })
  seoData?: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
  };

  @Prop({ type: Object, default: {} })
  analytics?: {
    impressions?: number;
    clicks?: number;
    contactRequests?: number;
  };

  // Virtual for category
  category?: any;

  // Virtual for template
  template?: any;
}

export const ItemImageSchema = SchemaFactory.createForClass(ItemImage);
export const ItemSchema = SchemaFactory.createForClass(Item);

// Add indexes
ItemSchema.index({ sellerId: 1 });
ItemSchema.index({ categoryId: 1 });
ItemSchema.index({ status: 1 });
ItemSchema.index({ price: 1 });
ItemSchema.index({ createdAt: -1 });
ItemSchema.index({ locationId: 1 });
ItemSchema.index({ isPromoted: 1, isFeatured: 1 });
ItemSchema.index({ expiresAt: 1 });

// Text search index
ItemSchema.index({
  title: 'text',
  description: 'text',
  tags: 'text',
});

// Compound indexes for common queries
ItemSchema.index({ status: 1, categoryId: 1, createdAt: -1 });
ItemSchema.index({ sellerId: 1, status: 1, createdAt: -1 });

// Virtual populate for category
ItemSchema.virtual('category', {
  ref: 'Category',
  localField: 'categoryId',
  foreignField: '_id',
  justOne: true,
});

// Virtual populate for template
ItemSchema.virtual('template', {
  ref: 'Template',
  localField: 'templateId',
  foreignField: '_id',
  justOne: true,
});

// Ensure virtual fields are serialized
ItemSchema.set('toJSON', { virtuals: true });
ItemSchema.set('toObject', { virtuals: true });
