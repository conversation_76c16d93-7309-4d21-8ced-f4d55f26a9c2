import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type TemplateDocument = Template & Document;

export enum FieldType {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  NUMBER = 'number',
  SELECT = 'select',
  MULTISELECT = 'multiselect',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  DATE = 'date',
  EMAIL = 'email',
  URL = 'url',
  FILE = 'file',
  IMAGE = 'image',
}

@Schema({ _id: false })
export class TemplateField {
  @Prop({ required: true })
  id: string;

  @Prop({ required: true, trim: true })
  name: string;

  @Prop({ required: true, trim: true })
  label: string;

  @Prop({ required: true, enum: FieldType })
  type: FieldType;

  @Prop({ default: false })
  required: boolean;

  @Prop({ trim: true })
  placeholder?: string;

  @Prop({ trim: true })
  helpText?: string;

  @Prop({ type: [String], default: [] })
  options: string[];

  @Prop({ type: Object, default: {} })
  validation?: Record<string, any>;

  @Prop({ default: 0 })
  sortOrder: number;

  @Prop({ default: true })
  isActive: boolean;
}

@Schema({ timestamps: true, collection: 'templates' })
export class Template {
  @Prop({ required: true, trim: true })
  name: string;

  @Prop({ trim: true })
  description?: string;

  @Prop({ type: Types.ObjectId, ref: 'Category', required: true })
  categoryId: Types.ObjectId;

  @Prop({ type: [TemplateField], default: [] })
  fields: TemplateField[];

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: 0 })
  sortOrder: number;

  @Prop({ type: Object, default: {} })
  metadata?: Record<string, any>;

  // Virtual for category
  category?: any;
}

export const TemplateFieldSchema = SchemaFactory.createForClass(TemplateField);
export const TemplateSchema = SchemaFactory.createForClass(Template);

// Add indexes
TemplateSchema.index({ categoryId: 1 });
TemplateSchema.index({ isActive: 1 });
TemplateSchema.index({ sortOrder: 1 });

// Virtual populate for category
TemplateSchema.virtual('category', {
  ref: 'Category',
  localField: 'categoryId',
  foreignField: '_id',
  justOne: true,
});

// Ensure virtual fields are serialized
TemplateSchema.set('toJSON', { virtuals: true });
TemplateSchema.set('toObject', { virtuals: true });
