const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3001;

// In-memory storage for verification codes
const verificationCodes = new Map();

app.use(cors());
app.use(express.json());

// Generate verification code endpoint
app.post('/auth/generate-verification-code', (req, res) => {
  const { email, type } = req.body;
  
  if (!email || !type) {
    return res.status(400).json({ message: 'Email and type are required' });
  }
  
  // Generate 6-digit code
  const code = Math.floor(100000 + Math.random() * 900000).toString();
  
  // Set expiration (15 minutes from now)
  const expiresAt = new Date();
  expiresAt.setMinutes(expiresAt.getMinutes() + 15);
  
  // Store code
  const key = `${email}:${type}`;
  verificationCodes.set(key, {
    code,
    expiresAt,
    isUsed: false,
  });
  
  console.log(`Generated verification code for ${email}: ${code}`);
  
  res.json({
    message: 'Verification code generated successfully',
    code: code, // For development - remove in production
    expiresAt,
  });
});

// Verify code endpoint
app.post('/auth/verify-code', (req, res) => {
  const { email, code, type } = req.body;
  
  if (!email || !code || !type) {
    return res.status(400).json({ message: 'Email, code, and type are required' });
  }
  
  const key = `${email}:${type}`;
  const storedCode = verificationCodes.get(key);
  
  if (!storedCode) {
    return res.status(400).json({ message: 'Invalid or expired verification code' });
  }
  
  if (storedCode.code !== code) {
    return res.status(400).json({ message: 'Invalid verification code' });
  }
  
  if (storedCode.isUsed) {
    return res.status(400).json({ message: 'Verification code has already been used' });
  }
  
  if (new Date() > storedCode.expiresAt) {
    return res.status(400).json({ message: 'Verification code has expired' });
  }
  
  // Mark as used
  storedCode.isUsed = true;
  
  res.json({ valid: true, message: 'Code verified successfully' });
});

// Register endpoint
app.post('/auth/register', (req, res) => {
  const { firstName, lastName, email, password, verificationCode } = req.body;
  
  if (!firstName || !lastName || !email || !password || !verificationCode) {
    return res.status(400).json({ message: 'All fields are required' });
  }
  
  // Check verification code
  const key = `${email}:registration`;
  const storedCode = verificationCodes.get(key);
  
  if (!storedCode || storedCode.code !== verificationCode || storedCode.isUsed) {
    return res.status(400).json({ message: 'Invalid or expired verification code' });
  }
  
  if (new Date() > storedCode.expiresAt) {
    return res.status(400).json({ message: 'Verification code has expired' });
  }
  
  // Mark code as used
  storedCode.isUsed = true;
  
  // Mock user creation
  const user = {
    id: Math.random().toString(36).substr(2, 9),
    email,
    firstName,
    lastName,
    role: 'BUYER',
    isVerified: true,
    createdAt: new Date(),
  };
  
  console.log(`User registered successfully: ${email}`);
  
  res.json({
    message: 'Account created successfully',
    user,
    // Mock tokens
    accessToken: 'mock-access-token',
    refreshToken: 'mock-refresh-token',
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.listen(PORT, () => {
  console.log(`🚀 Simple backend server running on http://localhost:${PORT}`);
  console.log('📧 Verification endpoints available:');
  console.log('  POST /auth/generate-verification-code');
  console.log('  POST /auth/verify-code');
  console.log('  POST /auth/register');
});
